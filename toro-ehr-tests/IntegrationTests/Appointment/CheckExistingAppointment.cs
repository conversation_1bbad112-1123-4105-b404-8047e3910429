using System.Net;
using IntegrationTests.Infrastructure;
using Shouldly;
using ToroEhr.Enums;
using ToroEhr.Features.Appointment;

namespace IntegrationTests.Appointment;

[Trait("Check Existing Appointment", "Happy Path")]
public class CheckExistingAppointmentHappyPath : IClassFixture<CheckExistingAppointmentFixture>
{
    private readonly CheckExistingAppointmentFixture _fixture;

    public CheckExistingAppointmentHappyPath(CheckExistingAppointmentFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Returns null when no existing appointment")]
    public void NoExistingAppointment()
    {
        _fixture.ExistingAppointmentResponse.ShouldBeNull();
    }
}

[Trait("Check Existing Appointment", "With Existing Appointment")]
public class CheckExistingAppointmentWithExisting : IClassFixture<CheckExistingAppointmentWithExistingFixture>
{
    private readonly CheckExistingAppointmentWithExistingFixture _fixture;

    public CheckExistingAppointmentWithExisting(CheckExistingAppointmentWithExistingFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Returns existing appointment info")]
    public void ExistingAppointmentReturned()
    {
        _fixture.ExistingAppointmentResponse.ShouldNotBeNull();
        _fixture.ExistingAppointmentResponse.AppointmentId.ShouldBe(_fixture.ExistingAppointmentId);
        _fixture.ExistingAppointmentResponse.Status.ShouldBe(AppointmentStatus.Confirmed);
        _fixture.ExistingAppointmentResponse.EmployeeName.ShouldBe($"{_fixture.EmployeeDoctor.FirstName} {_fixture.EmployeeDoctor.LastName}");
        _fixture.ExistingAppointmentResponse.LocationName.ShouldBe(_fixture.Location.Name);
    }
}

public class CheckExistingAppointmentFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public ExistingAppointmentResponse? ExistingAppointmentResponse;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        await Session.SaveChangesAsync();

        HttpClient client = CreateEmployeeDoctorClient();
        var tomorrow = DateTimeOffset.UtcNow.AddDays(1);
        Response = await client.GetAsync($"appointments/check-existing?patientId={Patient.Id}&employeeId={EmployeeDoctor.Id}&date={tomorrow:yyyy-MM-ddTHH:mm:ssZ}");
        ExistingAppointmentResponse = await GetRequestContent<ExistingAppointmentResponse?>(Response);
    }
}

public class CheckExistingAppointmentWithExistingFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public ExistingAppointmentResponse? ExistingAppointmentResponse;
    public string? ExistingAppointmentId;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);

        // create an existing appointment for the patient on the target date
        var tomorrow = DateTimeOffset.UtcNow.AddDays(1);
        var existingAppointment = ToroEhr.Domain.Appointment.Create(
            Patient.Id, 
            EmployeeDoctor.Id, 
            Location.Id, 
            null, // no encounter for this test
            tomorrow.Date.AddHours(10), // 10 AM
            tomorrow.Date.AddHours(10).AddMinutes(30), // 10:30 AM
            30, 
            AppointmentStatus.Confirmed);
        
        await Session.StoreAsync(existingAppointment);
        await Session.SaveChangesAsync();
        ExistingAppointmentId = existingAppointment.Id;

        HttpClient client = CreateEmployeeDoctorClient();
        Response = await client.GetAsync($"appointments/check-existing?patientId={Patient.Id}&employeeId={EmployeeDoctor.Id}&date={tomorrow:yyyy-MM-ddTHH:mm:ssZ}");
        ExistingAppointmentResponse = await GetRequestContent<ExistingAppointmentResponse?>(Response);
    }
}
