using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Enums;
using ToroEhr.Features.Appointment;
using ToroEhr.Features.Appointment.Events;
using ToroEhr.Features.Shared;

namespace IntegrationTests.Appointment;

[Trait("Cancel Appointment", "Happy Path")]
public class CancelAppointmentHappyPath : IClassFixture<CancelAppointmentFixture>
{
    private readonly CancelAppointmentFixture _fixture;

    public CancelAppointmentHappyPath(CancelAppointmentFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Appointment is canceled, not deleted")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Appointment> appointments =
            await session.Query<ToroEhr.Domain.Appointment>()
            .ToListAsync();

        appointments.ShouldHaveSingleItem();
        appointments.First().Status.ShouldBe(AppointmentStatus.Canceled.Name);
    }

    [Fact(DisplayName = "3. Associated encounter is also canceled")]
    public async Task EncounterIsCanceled()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Encounter> encounters =
            await session.Query<ToroEhr.Domain.Encounter>()
            .ToListAsync();

        encounters.ShouldHaveSingleItem();
        encounters.First().Status.ShouldBe(EncounterStatus.Canceled.Name);
    }
}

[Trait("Cancel Appointment", "Employee Cancellation Notifications")]
public class CancelAppointmentEmployeeNotifications : IClassFixture<CancelAppointmentEmployeeFixture>
{
    private readonly CancelAppointmentEmployeeFixture _fixture;

    public CancelAppointmentEmployeeNotifications(CancelAppointmentEmployeeFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Email notification event is created for patient with email preference")]
    public async Task EmailNotificationEventCreated()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        // Debug: Check patient's preferred contact method
        var patient = await session.LoadAsync<ToroEhr.Domain.Patient>(_fixture.Patient.Id);
        patient.ShouldNotBeNull();
        patient.PreferredContactMethod.ShouldBe("Email");

        List<BaseEventEntity> events = await session.Query<BaseEventEntity>(collectionName: "Events")
            .ToListAsync();

        // Debug: Check all events
        events.Count.ShouldBeGreaterThan(0, $"No events found. All events: {string.Join(", ", events.Select(e => e.GetType().Name))}");

        var emailEvents = events.OfType<SendEmailAppointmentCanceled>().ToList();
        emailEvents.ShouldHaveSingleItem($"Expected 1 email event, found {emailEvents.Count}. All events: {string.Join(", ", events.Select(e => e.GetType().Name))}");

        var emailEvent = emailEvents.First();
        emailEvent.Patient.Id.ShouldBe(_fixture.Patient.Id);
        emailEvent.Employee.Id.ShouldBe(_fixture.EmployeeDoctor.Id);
        emailEvent.Appointment.Id.ShouldBe(_fixture.AppointmentId);
    }

    [Fact(DisplayName = "3. In-app notification is created for patient")]
    public async Task InAppNotificationCreated()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Notification> notifications =
            await session.Query<ToroEhr.Domain.Notification>()
            .ToListAsync();

        var patientNotifications = notifications.Where(n => n.UserId == _fixture.Patient.Id).ToList();
        patientNotifications.ShouldHaveSingleItem();

        var notification = patientNotifications.First();
        notification.NotificationType.ShouldBe(NotificationType.AppointmentCanceled.Name);
        notification.Title.ShouldBe("Appointment Canceled");
        notification.Message.ShouldBe("Your appointment has been canceled.");
    }
}

public class CancelAppointmentFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public CancelAppointmentCommand? Command;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        HttpClient client = CreatePatientClient();

        // Create encounter first
        var encounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, DateTimeOffset.Now.AddDays(5));
        await Session.StoreAsync(encounter);

        var appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, encounter.Id,
            startAt: DateTimeOffset.Now.AddDays(5), endAt: DateTimeOffset.Now.AddDays(5).AddMinutes(30), 30, AppointmentStatus.Confirmed);
        await Session.StoreAsync(appointment);
        await Session.SaveChangesAsync();

        Command = new CancelAppointmentCommand(appointment.Id);
        StringContent requestContent = BuildRequestContent(Command);

        var request = new HttpRequestMessage(HttpMethod.Delete, "appointments")
        {
            Content = requestContent
        };

        Response = await client.SendAsync(request);

    }
}

public class CancelAppointmentEmployeeFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public CancelAppointmentCommand? Command;
    public string AppointmentId = string.Empty;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);

        // Set patient preferred contact method to email (default behavior)
        var address = Patient.Address ?? ToroEhr.Domain.Address.Create("123 Test St", "Test City", "Test State", "12345");
        Patient.SetContactInfo(new ToroEhr.Features.Patient.SetPatientContactInfoCommand(
            new ToroEhr.Shared.Models.AddressRequest(address.Street, address.City, address.State, address.ZipCode),
            null, // no previous address
            Patient.PhoneNumbers.Select(p => new ToroEhr.Features.Patient.PhoneNumberRequest(p.Number, p.Type, p.IsPrimary)).ToList(),
            Patient.Emails.Select(e => new ToroEhr.Features.Patient.EmailAddressRequest(e.Email, e.IsPrimary)).ToList(),
            Patient.PreferredContactName ?? "Test Patient",
            "Email", // Set preferred contact method to email
            Patient.SocialSecurityNumber ?? "",
            Patient.EmergencyContacts.Select(ec => new ToroEhr.Features.Patient.EmergencyContactRequest(ec.Name, ec.Relationship, ec.PhoneNumber, ec.Primary)).ToList()
        ));
        await Session.StoreAsync(Patient);
        await Session.SaveChangesAsync(); // Save the patient changes

        // Create encounter first
        var encounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, DateTimeOffset.Now.AddDays(5));
        await Session.StoreAsync(encounter);

        var appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, encounter.Id,
            startAt: DateTimeOffset.Now.AddDays(5), endAt: DateTimeOffset.Now.AddDays(5).AddMinutes(30), 30, AppointmentStatus.Confirmed);
        await Session.StoreAsync(appointment);
        await Session.SaveChangesAsync();

        AppointmentId = appointment.Id;

        // Use employee client to cancel appointment (employee-initiated cancellation)
        HttpClient client = CreateEmployeeDoctorClient();
        Command = new CancelAppointmentCommand(appointment.Id);
        StringContent requestContent = BuildRequestContent(Command);

        var request = new HttpRequestMessage(HttpMethod.Delete, "appointments")
        {
            Content = requestContent
        };

        Response = await client.SendAsync(request);
    }
}

[Trait("Cancel Appointment", "Employee Cancellation SMS Notifications")]
public class CancelAppointmentEmployeeSmsNotifications : IClassFixture<CancelAppointmentEmployeeSmsFixture>
{
    private readonly CancelAppointmentEmployeeSmsFixture _fixture;

    public CancelAppointmentEmployeeSmsNotifications(CancelAppointmentEmployeeSmsFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. SMS notification event is created for patient with text preference")]
    public async Task SmsNotificationEventCreated()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<BaseEventEntity> events = await session.Query<BaseEventEntity>(collectionName: "Events")
            .ToListAsync();

        var smsEvents = events.OfType<SendSmsAppointmentCanceled>().ToList();
        smsEvents.ShouldHaveSingleItem();

        var smsEvent = smsEvents.First();
        smsEvent.Patient.Id.ShouldBe(_fixture.Patient.Id);
        smsEvent.Employee.Id.ShouldBe(_fixture.EmployeeDoctor.Id);
        smsEvent.Appointment.Id.ShouldBe(_fixture.AppointmentId);
    }
}

public class CancelAppointmentEmployeeSmsFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public CancelAppointmentCommand? Command;
    public string AppointmentId = string.Empty;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);

        // Set patient preferred contact method to Text
        var address = Patient.Address ?? ToroEhr.Domain.Address.Create("123 Test St", "Test City", "Test State", "12345");
        Patient.SetContactInfo(new ToroEhr.Features.Patient.SetPatientContactInfoCommand(
            new ToroEhr.Shared.Models.AddressRequest(address.Street, address.City, address.State, address.ZipCode),
            null, // no previous address
            Patient.PhoneNumbers.Select(p => new ToroEhr.Features.Patient.PhoneNumberRequest(p.Number, p.Type, p.IsPrimary)).ToList(),
            Patient.Emails.Select(e => new ToroEhr.Features.Patient.EmailAddressRequest(e.Email, e.IsPrimary)).ToList(),
            Patient.PreferredContactName ?? "Test Patient",
            "Text", // Set preferred contact method to Text
            Patient.SocialSecurityNumber ?? "",
            Patient.EmergencyContacts.Select(ec => new ToroEhr.Features.Patient.EmergencyContactRequest(ec.Name, ec.Relationship, ec.PhoneNumber, ec.Primary)).ToList()
        ));
        await Session.StoreAsync(Patient);
        await Session.SaveChangesAsync(); // Save the patient changes

        // Create encounter first
        var encounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, DateTimeOffset.Now.AddDays(5));
        await Session.StoreAsync(encounter);

        var appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, encounter.Id,
            startAt: DateTimeOffset.Now.AddDays(5), endAt: DateTimeOffset.Now.AddDays(5).AddMinutes(30), 30, AppointmentStatus.Confirmed);
        await Session.StoreAsync(appointment);
        await Session.SaveChangesAsync();

        AppointmentId = appointment.Id;

        // Use employee client to cancel appointment (employee-initiated cancellation)
        HttpClient client = CreateEmployeeDoctorClient();
        Command = new CancelAppointmentCommand(appointment.Id);
        StringContent requestContent = BuildRequestContent(Command);

        var request = new HttpRequestMessage(HttpMethod.Delete, "appointments")
        {
            Content = requestContent
        };

        Response = await client.SendAsync(request);
    }
}