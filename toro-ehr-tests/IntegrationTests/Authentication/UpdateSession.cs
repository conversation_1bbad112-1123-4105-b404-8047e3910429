using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Authentication;

namespace IntegrationTests.Authentication;

internal class UpdateSession
{
}
[Trait("Edit Appointment", "Happy Path")]
public class UpdateSessionHappyPath : IClassFixture<UpdateSessionFixture>
{
    private readonly UpdateSessionFixture _fixture;

    public UpdateSessionHappyPath(UpdateSessionFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Session has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        User user =
            await session.Query<User>()
            .Where(x => x.EmployeeId == _fixture.EmployeeDoctor.Id)
            .FirstOrDefaultAsync();

        UserSession userSession =
            await session.Query<UserSession>()
            .Where(x => x.UserId == user.Id)
            .FirstOrDefaultAsync();

        userSession.LocationId.ShouldBe(_fixture.NewLocation!.Id);
    }
}

public class UpdateSessionFixture : BaseFixture
{
    public Location? NewLocation;
    public HttpResponseMessage? Response;
    public UpdateSessionCommand? Command;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);



        NewLocation = ToroEhr.Domain.Location.Create(Organization.Id, "Location 2", "Ambulatory", false, string.Empty,
            string.Empty, 0, 0, 48, true, null, null);
        await Session.StoreAsync(NewLocation);

        var locationEmployee = ToroEhr.Domain.LocationEmployee.Create(Organization.Id, NewLocation.Id,
            EmployeeDoctor.Id, [EmployeeRole.Practitioner], string.Empty, string.Empty, string.Empty, DateTime.UtcNow,
            false, "Dermatology");
        await Session.StoreAsync(locationEmployee);
        await Session.SaveChangesAsync();

        HttpClient client = CreateEmployeeDoctorClient();

        Command = new UpdateSessionCommand(UserRole.Employee, Organization.Id, NewLocation.Id);
        StringContent requestContent = BuildRequestContent(Command);
        Response = await client.PostAsync("authentication/update-session", requestContent);
    }
}