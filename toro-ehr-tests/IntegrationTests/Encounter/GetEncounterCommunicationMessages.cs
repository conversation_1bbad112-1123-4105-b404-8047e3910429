using IntegrationTests.Infrastructure;
using Shouldly;
using System.Net;
using ToroEhr.Features.Encounter;
using ToroEhr.ValueObjects;

namespace IntegrationTests.Encounter;

[Trait("Get Encounter Communication Messages", "Happy Path")]
public class GetEncounterCommunicationMessagesHappyPath : IClassFixture<GetEncounterCommunicationMessagesFixture>
{
    private readonly GetEncounterCommunicationMessagesFixture _fixture;

    public GetEncounterCommunicationMessagesHappyPath(GetEncounterCommunicationMessagesFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Encounter Communication has expected content")]
    public async Task ExpectedContent()
    {
        var settings = new VerifySettings();
        settings.IgnoreMembers("Id", "PatientId");
        await Verify(_fixture.ListOfEncounterCommunicationMessageResponses, settings);
    }
}

public class GetEncounterCommunicationMessagesFixture : BaseFixture
{
    public List<EncounterCommunicationMessageResponse>? ListOfEncounterCommunicationMessageResponses;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        // given
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        

        var encounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, DateTimeOffset.Now);
        await Session.StoreAsync(encounter);

        var encounterEmail = ToroEhr.Domain.EncounterEmail.Create(Patient.Id, new Sender(false, EmployeeDoctor.Id, EmployeeDoctor.FullName,
            EmployeeDoctor.Email), encounter.Id, "Test Subject", "Test Message", DateTimeOffset.UtcNow, [], null);
        await Session.StoreAsync(encounterEmail);

        await Session.SaveChangesAsync();

        // when
        HttpClient client = CreateEmployeeDoctorClient();
        Response = await client.GetAsync($"/encounter/{encounter.Id}/communication");
        ListOfEncounterCommunicationMessageResponses = await GetRequestContent<List<EncounterCommunicationMessageResponse>>(Response);
    }
}