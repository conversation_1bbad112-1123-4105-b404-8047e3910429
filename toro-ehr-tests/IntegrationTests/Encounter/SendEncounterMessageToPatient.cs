using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Encounter;
using ToroEhr.ValueObjects;

namespace IntegrationTests.Encounter;

internal class SendEncounterMessageToPatient
{
}
[Trait("Send Encounter Message To Patient", "Happy Path")]
public class SendEncounterMessageToPatientHappyPath : IClassFixture<SendEncounterMessageToPatientFixture>
{
    private readonly SendEncounterMessageToPatientFixture _fixture;

    public SendEncounterMessageToPatientHappyPath(SendEncounterMessageToPatientFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Encounter Email has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<EncounterEmail> encounterEmails =
            await session.Query<EncounterEmail>()
            .ToListAsync();

        var settings = new VerifySettings();
        settings.IgnoreMembers("Id", "PatientId", "EncounterId");
        settings.IgnoreMember<Sender>(x => x.SenderId);
        await Verify(encounterEmails, settings);
    }

    [Fact(DisplayName = "3. Notification is created for patient")]
    public async Task NotificationCreatedForPatient()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Notification> notifications =
            await session.Query<ToroEhr.Domain.Notification>()
            .Where(x => x.UserId == _fixture.Patient.Id)
            .ToListAsync();

        notifications.ShouldNotBeEmpty();
        var messageNotification = notifications.FirstOrDefault(x => x.NotificationType == NotificationType.MessageReceived.Name);
        messageNotification.ShouldNotBeNull();
        messageNotification.Title.ShouldBe("New Message Received");
        messageNotification.Message.ShouldContain("email message from Test User");
        messageNotification.RelatedEntityType.ShouldBe("Encounter");
        messageNotification.Status.ShouldBe(NotificationStatus.Unread.Name);
    }
}

public class SendEncounterMessageToPatientFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public SendEncounterMessageCommand? Command;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);

        var encounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, DateTimeOffset.Now);
        await Session.StoreAsync(encounter);

        await Session.SaveChangesAsync();

        HttpClient client = CreateEmployeeDoctorClient();

        using var formData = new MultipartFormDataContent
        {
            { new StringContent(MessageType.Email.Name), "messageType" },
            { new StringContent(Patient.Id), "patientId" },
            { new StringContent(encounter.Id), "encounterId" },
            { new StringContent("Test Subject"), "subject" },
            { new StringContent("This is a test message."), "message" }
        };

        Response = await client.PostAsync($"encounter/{encounter.Id}/communication", formData);
    }
}