using IntegrationTests.Infrastructure;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Enums;
using ToroEhr.Features.Encounter;

namespace IntegrationTests.Encounter;

[Trait("Checkout Encounter", "Happy Path")]
public class CheckoutEncounterHappyPath : IClassFixture<CheckoutEncounterFixture>
{
    private readonly CheckoutEncounterFixture _fixture;

    public CheckoutEncounterHappyPath(CheckoutEncounterFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Encounter status is updated to Completed")]
    public async Task EncounterStatusUpdated()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        var encounter = await session.LoadAsync<ToroEhr.Domain.Encounter>(_fixture.EncounterId);
        encounter.ShouldNotBeNull();
        encounter.Status.ShouldBe(EncounterStatus.Completed.Name);
        encounter.CompletedAt.ShouldNotBeNull();
    }

    [Fact(DisplayName = "3. Appointment status is updated to Completed")]
    public async Task AppointmentStatusUpdated()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        var appointment = await session.LoadAsync<ToroEhr.Domain.Appointment>(_fixture.AppointmentId);
        appointment.ShouldNotBeNull();
        appointment.Status.ShouldBe(AppointmentStatus.Completed.Name);
    }
}

public class CheckoutEncounterFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public string? EncounterId;
    public string? AppointmentId;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);

        // create encounter
        var encounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, DateTimeOffset.Now.AddMinutes(-30));
        await Session.StoreAsync(encounter);
        EncounterId = encounter.Id;

        // create appointment linked to the encounter
        var appointment = ToroEhr.Domain.Appointment.Create(
            Patient.Id,
            EmployeeDoctor.Id,
            Location.Id,
            encounter.Id,
            DateTimeOffset.Now.AddMinutes(-30),
            DateTimeOffset.Now,
            30,
            AppointmentStatus.CheckedIn);

        await Session.StoreAsync(appointment);
        AppointmentId = appointment.Id;

        await Session.SaveChangesAsync();

        // checkout the encounter
        HttpClient client = CreateEmployeeDoctorClient();
        var command = new CheckoutEncounterCommand(encounter.Id);
        StringContent requestContent = BuildRequestContent(command);
        Response = await client.PostAsync("/encounter/checkout", requestContent);
    }
}
