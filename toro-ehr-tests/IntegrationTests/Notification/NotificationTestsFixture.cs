using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Services;
using IntegrationTests.Infrastructure;

namespace IntegrationTests.Notification;

public class NotificationTestsFixture : BaseFixture
{
    public string AppointmentId { get; private set; } = null!;
    public string PatientNotificationId { get; private set; } = null!;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();

        using IAsyncDocumentSession session = DocumentStore.OpenAsyncSession();

        // create an appointment
        var appointment = ToroEhr.Domain.Appointment.Create(
            Patient.Id,
            EmployeeDoctor.Id,
            Location.Id,
            null, // no encounter for test
            DateTimeOffset.UtcNow.AddDays(1),
            DateTimeOffset.UtcNow.AddDays(1).AddHours(1),
            60, // duration in minutes
            AppointmentStatus.Confirmed);

        await session.StoreAsync(appointment);
        AppointmentId = appointment.Id;

        // create a notification for the patient
        await NotificationService.CreateAppointmentConfirmedNotification(
            session,
            Patient.Id,
            appointment.Id,
            DateTimeOffset.UtcNow.DateTime);

        // we'll need to query for the notification ID in tests
        PatientNotificationId = ""; // placeholder for now

        await session.SaveChangesAsync();
    }
}
