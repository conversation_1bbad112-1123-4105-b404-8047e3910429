using Raven.Client.Documents.Session;
using Shouldly;
using ToroEhr.Enums;
using ToroEhr.Features.Notification;
using ToroEhr.Services;
using ToroEhr.Shared;

namespace IntegrationTests.Notification;

public class NotificationTests : IClassFixture<NotificationTestsFixture>
{
    private readonly NotificationTestsFixture _fixture;

    public NotificationTests(NotificationTestsFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Should create appointment confirmed notification for patient")]
    public async Task ShouldCreateAppointmentConfirmedNotification()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        // verify notification was created
        var notification = await session.LoadAsync<ToroEhr.Domain.Notification>(_fixture.PatientNotificationId);
        notification.ShouldNotBeNull();
        notification.UserId.ShouldBe(_fixture.Patient.Id);
        notification.UserType.ShouldBe(UserRole.Patient.Name);
        notification.NotificationType.ShouldBe(NotificationType.AppointmentConfirmed.Name);
        notification.Status.ShouldBe(NotificationStatus.Unread.Name);
        notification.Title.ShouldBe("Appointment Confirmed");
        notification.RelatedEntityId.ShouldBe(_fixture.AppointmentId);
        notification.RelatedEntityType.ShouldBe("Appointment");
    }

    [Fact(DisplayName = "2. Should get user notifications with pagination")]
    public async Task ShouldGetUserNotificationsWithPagination()
    {
        var client = _fixture.CreatePatientClient();
        var response = await client.GetAsync("api/notification?pageNumber=1&pageSize=10");
        
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        content.ShouldContain("Appointment Confirmed");
    }

    [Fact(DisplayName = "3. Should get unread notification count")]
    public async Task ShouldGetUnreadNotificationCount()
    {
        var client = _fixture.CreatePatientClient();
        var response = await client.GetAsync("api/notification/unread-count");
        
        response.EnsureSuccessStatusCode();
        var count = await response.Content.ReadAsStringAsync();
        count.ShouldBe("1"); // one unread notification
    }

    [Fact(DisplayName = "4. Should mark notification as read")]
    public async Task ShouldMarkNotificationAsRead()
    {
        var client = _fixture.CreatePatientClient();
        var response = await client.PutAsync($"api/notification/{_fixture.PatientNotificationId}/read", null);
        
        response.EnsureSuccessStatusCode();

        // verify notification is marked as read
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();
        var notification = await session.LoadAsync<ToroEhr.Domain.Notification>(_fixture.PatientNotificationId);
        notification.Status.ShouldBe(NotificationStatus.Read.Name);
        notification.ReadAt.ShouldNotBeNull();
    }

    [Fact(DisplayName = "5. Should delete notification")]
    public async Task ShouldDeleteNotification()
    {
        var client = _fixture.CreatePatientClient();
        var response = await client.DeleteAsync($"api/notification/{_fixture.PatientNotificationId}");
        
        response.EnsureSuccessStatusCode();

        // verify notification is deleted
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();
        var notification = await session.LoadAsync<ToroEhr.Domain.Notification>(_fixture.PatientNotificationId);
        notification.ShouldBeNull();
    }
}
