using System.Net;
using IntegrationTests.Infrastructure;
using Raven.Client.Documents.Session;
using Shouldly;
using ToroEhr.Features.Organization;

namespace IntegrationTests.Organization;

[Trait("Edit Organizations", "Happy Path")]
public class EditOrganizationHappyPath : IClassFixture<EditOrganizationFixture>
{
    private readonly EditOrganizationFixture _fixture;

    public EditOrganizationHappyPath(EditOrganizationFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Organization has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        ToroEhr.Domain.Organization matchedOrg =
            await session.LoadAsync<ToroEhr.Domain.Organization>(_fixture.EditOrganizationCommand!.Id);

        matchedOrg.ShouldNotBeNull();
        matchedOrg.Name.ShouldBe(_fixture.EditOrganizationCommand!.Name);
    }
}

public class EditOrganizationFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public EditOrganizationCommand? EditOrganizationCommand;

    public override async Task InitializeAsync()
    {
        // given
        await base.InitializeAsync();

        // when
        HttpClient client = CreateSuperAdminClient();
        EditOrganizationCommand = new EditOrganizationCommand(Organization.Id, "TestOrg", EmployeeAdmin.Id, Location.Id, null);
        StringContent requestContent = BuildRequestContent(EditOrganizationCommand);
        Response = await client.PutAsync("organizations", requestContent);
    }
}