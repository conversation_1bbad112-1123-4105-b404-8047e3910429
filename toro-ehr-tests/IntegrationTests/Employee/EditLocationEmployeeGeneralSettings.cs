using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Features.Employee;

namespace IntegrationTests.Employee;

[Trait("Edit Location Employee General Settings", "Happy Path")]
public class EditLocationEmployeeGeneralSettingsHappyPath : IClassFixture<EditLocationEmployeeGeneralSettingsFixture>
{
    private readonly EditLocationEmployeeGeneralSettingsFixture _fixture;

    public EditLocationEmployeeGeneralSettingsHappyPath(EditLocationEmployeeGeneralSettingsFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Response has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        ToroEhr.Domain.LocationEmployee matchedOrgPra =
            await session.Query<ToroEhr.Domain.LocationEmployee>()
            .Where(x => x.EmployeeId == _fixture.EmployeeDoctor!.Id && x.OrganizationId == _fixture.Organization!.Id)
            .FirstOrDefaultAsync();

        matchedOrgPra.ShouldNotBeNull();
        matchedOrgPra.CalendarColor.ShouldBe(_fixture.EditGeneralSettingsRequest!.CalendarColor);
        matchedOrgPra.NumberOfAppointmentOverlaps.ShouldBe(_fixture.EditGeneralSettingsRequest!.NumberOfAppointmentOverlaps);
        matchedOrgPra.AppointmentDurationInMinutes.ShouldBe(_fixture.EditGeneralSettingsRequest!.AppointmentDurationInMinutes);
        matchedOrgPra.ReceivedNotificationPreferences.ShouldBe(_fixture.EditGeneralSettingsRequest!.ReceivedNotificationPreferences);
    }
}

public class EditLocationEmployeeGeneralSettingsFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public EditGeneralSettingsRequest? EditGeneralSettingsRequest;

    public override async Task InitializeAsync()
    {
        // given
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        await Session.SaveChangesAsync();

        // when
        HttpClient client = CreateEmployeeDoctorClient();
        EditGeneralSettingsRequest = new EditGeneralSettingsRequest("blue", 3, false, 30, new List<string>(), "General Practice");
        StringContent requestContent = BuildRequestContent(EditGeneralSettingsRequest);
        Response = await client.PutAsync($"employees/{EmployeeDoctor.Id}/locations/{Location.Id}/general-settings", requestContent);
    }
}