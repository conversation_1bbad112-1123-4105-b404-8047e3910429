using System.Net;
using IntegrationTests.Infrastructure;
using Raven.Client.Documents.Session;
using Shouldly;
using ToroEhr.Enums;
using ToroEhr.Features.Employee;

namespace IntegrationTests.Employee;

[Trait("Get Available Appointment Slots", "Happy Path")]
public class GetAvailableAppointmentSlotsHappyPath : IClassFixture<GetAvailableAppointmentSlotsFixture>
{
    private readonly GetAvailableAppointmentSlotsFixture _fixture;

    public GetAvailableAppointmentSlotsHappyPath(GetAvailableAppointmentSlotsFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Available slots are returned when no existing appointment")]
    public void AvailableSlotsReturned()
    {
        _fixture.SlotsResponse.ShouldNotBeNull();
        _fixture.SlotsResponse.AvailableSlots.ShouldNotBeEmpty();
        _fixture.SlotsResponse.ExistingAppointment.ShouldBeNull();
    }
}

[Trait("Get Available Appointment Slots", "With Existing Appointment")]
public class GetAvailableAppointmentSlotsWithExistingAppointment : IClassFixture<GetAvailableAppointmentSlotsWithExistingAppointmentFixture>
{
    private readonly GetAvailableAppointmentSlotsWithExistingAppointmentFixture _fixture;

    public GetAvailableAppointmentSlotsWithExistingAppointment(GetAvailableAppointmentSlotsWithExistingAppointmentFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. No available slots when patient has existing appointment")]
    public void NoAvailableSlots()
    {
        _fixture.SlotsResponse.ShouldNotBeNull();
        _fixture.SlotsResponse.AvailableSlots.ShouldBeEmpty();
        _fixture.SlotsResponse.ExistingAppointment.ShouldNotBeNull();
    }

    [Fact(DisplayName = "3. Existing appointment info is populated correctly")]
    public void ExistingAppointmentInfo()
    {
        _fixture.SlotsResponse.ShouldNotBeNull();
        var existingAppointment = _fixture.SlotsResponse.ExistingAppointment;
        
        existingAppointment.ShouldNotBeNull();
        existingAppointment.AppointmentId.ShouldBe(_fixture.ExistingAppointmentId);
        existingAppointment.Status.ShouldBe(AppointmentStatus.Confirmed.Name);
        existingAppointment.EmployeeName.ShouldBe($"{_fixture.EmployeeDoctor.FirstName} {_fixture.EmployeeDoctor.LastName}");
        existingAppointment.LocationName.ShouldBe(_fixture.Location.Name);
    }
}

public class GetAvailableAppointmentSlotsFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public AvailableAppointmentSlotsResponse? SlotsResponse;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        await Session.SaveChangesAsync();

        HttpClient client = CreatePatientClient();
        var tomorrow = DateTimeOffset.UtcNow.AddDays(1);
        Response = await client.GetAsync($"employees/{EmployeeDoctor.Id}/locations/{Location.Id}/appointment-slots?date={tomorrow:yyyy-MM-ddTHH:mm:ssZ}");
        SlotsResponse = await GetRequestContent<AvailableAppointmentSlotsResponse>(Response);
    }
}

public class GetAvailableAppointmentSlotsWithExistingAppointmentFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public AvailableAppointmentSlotsResponse? SlotsResponse;
    public string? ExistingAppointmentId;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);

        // create an existing appointment for the patient on the target date
        var tomorrow = DateTimeOffset.UtcNow.AddDays(1);
        var existingAppointment = ToroEhr.Domain.Appointment.Create(
            Patient.Id, 
            EmployeeDoctor.Id, 
            Location.Id, 
            null, // no encounter for this test
            tomorrow.Date.AddHours(10), // 10 AM
            tomorrow.Date.AddHours(10).AddMinutes(30), // 10:30 AM
            30, 
            AppointmentStatus.Confirmed);
        
        await Session.StoreAsync(existingAppointment);
        await Session.SaveChangesAsync();
        ExistingAppointmentId = existingAppointment.Id;

        HttpClient client = CreatePatientClient();
        Response = await client.GetAsync($"employees/{EmployeeDoctor.Id}/locations/{Location.Id}/appointment-slots?date={tomorrow:yyyy-MM-ddTHH:mm:ssZ}");
        SlotsResponse = await GetRequestContent<AvailableAppointmentSlotsResponse>(Response);
    }
}
