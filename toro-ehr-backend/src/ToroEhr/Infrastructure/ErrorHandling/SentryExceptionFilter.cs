using System.Reflection;
using System.Security.Authentication;
using Sentry;
using ToroEhr.Infrastructure.Exceptions;

namespace ToroEhr.Infrastructure.ErrorHandling;

/// <summary>
/// filters out intentional business exceptions from being sent to Sentry
/// while allowing genuine unexpected errors to be captured
/// </summary>
public static class SentryExceptionFilter
{
    /// <summary>
    /// determines if an exception should be sent to Sentry
    /// returns false for intentional business exceptions that are handled by GlobalExceptionHandler
    /// </summary>
    public static bool ShouldCaptureException(Exception exception)
    {
        return exception switch
        {
            // exclude intentional business exceptions - these are expected and handled
            ValidationException => false,
            PaymentException => false,
            NotFoundException => false,
            AuthenticationException => false,
            UnauthorizedAccessException => false,
            
            // capture all other exceptions as they represent unexpected errors
            _ => true
        };
    }

    /// <summary>
    /// configures Sentry to use the exception filter and additional settings
    /// </summary>
    public static void ConfigureExceptionFilter(SentryOptions options)
    {
        options.SetBeforeSend((sentryEvent, hint) =>
        {
            // check if there's an exception in the event
            if (sentryEvent.Exception != null)
            {
                // filter out business exceptions
                if (!ShouldCaptureException(sentryEvent.Exception))
                {
                    return null; // don't send to Sentry
                }
            }

            // add custom tags for better organization
            sentryEvent.SetTag("application", "toro-ehr");

            // remove sensitive data from request body if present
            if (sentryEvent.Request?.Data != null)
            {
                sentryEvent.Request.Data = "[Filtered]";
            }

            return sentryEvent; // send to Sentry
        });

        // configure what data to include/exclude
        options.SendDefaultPii = false; // don't send personally identifiable information
        options.IsGlobalModeEnabled = true; // capture unhandled exceptions globally
        options.MaxBreadcrumbs = 50; // keep more breadcrumbs for better debugging context

        // add release information if available
        var version = Assembly.GetExecutingAssembly()
            .GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion;
        if (!string.IsNullOrEmpty(version))
        {
            options.Release = $"toro-ehr@{version}";
        }
    }
}
