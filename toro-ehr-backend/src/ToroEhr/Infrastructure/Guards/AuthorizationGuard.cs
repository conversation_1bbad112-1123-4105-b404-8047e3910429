using ToroEhr.Enums;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Infrastructure.Guards;

public static class AuthorizationGuard
{
    public static void AffirmRole(UserRequestSession userRequestSession, List<EmployeeRole> allowedRoles)
    {
        if (allowedRoles.IsEmpty())
            throw new ArgumentException(nameof(allowedRoles));

        if (userRequestSession.SelectedLocationEmployeeRoles.IsEmpty())
            throw new UnauthorizedAccessException();

        var hasAllowedRole = userRequestSession.SelectedLocationEmployeeRoles?
            .Select(roleName => EmployeeRole.FromName(roleName))
            .Any(allowedRoles.Contains);

        if (hasAllowedRole != true)
            throw new UnauthorizedAccessException();
    }

    public static void AffirmIsEmployee(UserRequestSession userRequestSession)
    {
        if (userRequestSession.SelectedUserRole != UserRole.Employee)
            throw new UnauthorizedAccessException();
    }

    public static void AffirmIsPractitioner(UserRequestSession userRequestSession)
    {
        if (userRequestSession.SelectedLocationEmployeeRoles != null &&
            userRequestSession.SelectedLocationEmployeeRoles.All(x => x != EmployeeRole.Practitioner.Name))
            throw new UnauthorizedAccessException();
    }


    public static void AffirmIsPatient(UserRequestSession userRequestSession)
    {
        if (userRequestSession.SelectedUserRole != UserRole.Patient)
            throw new UnauthorizedAccessException();
    }
    
    public static void AffirmIsSuperAdmin(UserRequestSession userRequestSession)
    {
        if (userRequestSession.SelectedUserRole != UserRole.SuperAdmin)
            throw new UnauthorizedAccessException();
    }

    public static void AffirmIsPatientOrEmployee(UserRequestSession userRequestSession)
    {
        if (userRequestSession.SelectedUserRole != UserRole.Patient && userRequestSession.SelectedLocationEmployeeRoles.IsEmpty())
            throw new UnauthorizedAccessException();
    }
}