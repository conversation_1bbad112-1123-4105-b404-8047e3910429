using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class EncounterStatus : SmartEnum<EncounterStatus, string>
{
    public static readonly EncounterStatus Planned = new EncounterStatus(nameof(Planned), "Planned");
    public static readonly EncounterStatus CheckedIn = new EncounterStatus(nameof(CheckedIn), "Checked In");
    public static readonly EncounterStatus Arrived = new EncounterStatus(nameof(Arrived), "Arrived");
    public static readonly EncounterStatus InProgress = new EncounterStatus(nameof(InProgress), "In Progress");
    public static readonly EncounterStatus Completed = new EncounterStatus(nameof(Completed), "Completed");
    public static readonly EncounterStatus Canceled = new EncounterStatus(nameof(Canceled), "Canceled");
    public static readonly EncounterStatus CanceledLate = new EncounterStatus(nameof(CanceledLate), "Canceled Late");
    public static readonly EncounterStatus Missed = new EncounterStatus(nameof(Missed), "Missed");

    private EncounterStatus(string name, string value) : base(name, value)
    {
    }
}