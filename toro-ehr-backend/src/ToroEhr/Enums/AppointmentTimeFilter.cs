using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class AppointmentTimeFilter : SmartEnum<AppointmentTimeFilter, string>
{
    public static readonly AppointmentTimeFilter All = new AppointmentTimeFilter(nameof(All), "All");
    public static readonly AppointmentTimeFilter Upcoming = new AppointmentTimeFilter(nameof(Upcoming), "Upcoming");
    public static readonly AppointmentTimeFilter Past = new AppointmentTimeFilter(nameof(Past), "Past");

    private AppointmentTimeFilter(string name, string value) : base(name, value)
    {
    }
}
