using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class QuestionnaireType : SmartEnum<QuestionnaireType, string>
{
    public static readonly QuestionnaireType History = new QuestionnaireType(nameof(History), "History");
    public static readonly QuestionnaireType VisitReason = new QuestionnaireType(nameof(VisitReason), "Visit Reason");

    private QuestionnaireType(string name, string value) : base(name, value)
    {
    }
}
