using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class NotificationStatus : SmartEnum<NotificationStatus, string>
{
    public static readonly NotificationStatus Unread = new NotificationStatus(nameof(Unread), "Unread");
    public static readonly NotificationStatus Read = new NotificationStatus(nameof(Read), "Read");

    private NotificationStatus(string name, string value) : base(name, value)
    {
    }
}
