using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class AppointmentStatus : SmartEnum<AppointmentStatus, string>
{
    public static readonly AppointmentStatus Confirmed = new AppointmentStatus(nameof(Confirmed), "Confirmed");
    public static readonly AppointmentStatus Pending = new AppointmentStatus(nameof(Pending), "Pending");
    public static readonly AppointmentStatus Canceled = new AppointmentStatus(nameof(Canceled), "Canceled");
    public static readonly AppointmentStatus CanceledLate = new AppointmentStatus(nameof(CanceledLate), "Canceled Late");
    public static readonly AppointmentStatus Missed = new AppointmentStatus(nameof(Missed), "Missed");
    public static readonly AppointmentStatus CheckedIn = new AppointmentStatus(nameof(CheckedIn), "Checked In");
    public static readonly AppointmentStatus InProgress = new AppointmentStatus(nameof(InProgress), "In Progress");
    public static readonly AppointmentStatus Completed = new AppointmentStatus(nameof(Completed), "Completed");

    // static lists for filtering
    public static readonly AppointmentStatus[] Upcoming = [Pending, Confirmed, CheckedIn, InProgress];
    public static readonly AppointmentStatus[] Past = [Completed, Missed, Canceled, CanceledLate];

    private AppointmentStatus(string name, string value) : base(name, value)
    {
    }
}