using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class NotificationType : SmartEnum<NotificationType, string>
{
    public static readonly NotificationType AppointmentConfirmed = new NotificationType(nameof(AppointmentConfirmed), "Appointment Confirmed");
    public static readonly NotificationType AppointmentRequested = new NotificationType(nameof(AppointmentRequested), "Appointment Requested");
    public static readonly NotificationType AppointmentRescheduled = new NotificationType(nameof(AppointmentRescheduled), "Appointment Rescheduled");
    public static readonly NotificationType AppointmentCanceled = new NotificationType(nameof(AppointmentCanceled), "Appointment Canceled");
    public static readonly NotificationType AppointmentMissed = new NotificationType(nameof(AppointmentMissed), "Appointment Missed");
    public static readonly NotificationType CompleteProfile = new NotificationType(nameof(CompleteProfile), "Complete Profile");
    public static readonly NotificationType CompleteQuestionnaires = new NotificationType(nameof(CompleteQuestionnaires), "Complete Questionnaires");
    public static readonly NotificationType QuestionnaireUpdated = new NotificationType(nameof(QuestionnaireUpdated), "Questionnaire Updated");
    public static readonly NotificationType MessageReceived = new NotificationType(nameof(MessageReceived), "Message Received");

    private NotificationType(string name, string value) : base(name, value)
    {
    }
}
