using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class ReceiveNotificationPreferencesType : SmartEnum<ReceiveNotificationPreferencesType, string>
{
    public static readonly ReceiveNotificationPreferencesType Booked = new ReceiveNotificationPreferencesType(nameof(Booked), "Booked");
    public static readonly ReceiveNotificationPreferencesType Rescheduled = new ReceiveNotificationPreferencesType(nameof(Rescheduled), "Rescheduled");
    public static readonly ReceiveNotificationPreferencesType Canceled = new ReceiveNotificationPreferencesType(nameof(Canceled), "Canceled");
    public static readonly ReceiveNotificationPreferencesType Missed = new ReceiveNotificationPreferencesType(nameof(Missed), "Missed");

    private ReceiveNotificationPreferencesType(string name, string value) : base(name, value)
    {
    }
}