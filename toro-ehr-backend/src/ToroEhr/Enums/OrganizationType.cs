using Ardalis.SmartEnum;
using ToroEhr.Domain;

namespace ToroEhr.Enums;

public sealed class OrganizationType : SmartEnum<OrganizationType, string>
{
    public static readonly OrganizationType NonPrescribingOrganization = new OrganizationType(nameof(NonPrescribingOrganization), "Non-Prescribing Organization");
    public static readonly OrganizationType PrescribingOrganization = new OrganizationType(nameof(PrescribingOrganization), "Prescribing  Organization");
    public static readonly OrganizationType Hospital = new OrganizationType(nameof(Hospital), "Hospital");

    private OrganizationType(string name, string value) : base(name, value)
    {
    }
}