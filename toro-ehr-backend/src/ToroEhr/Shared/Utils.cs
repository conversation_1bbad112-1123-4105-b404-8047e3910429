using NodaTime;
using System.Security.Cryptography;

namespace ToroEhr.Shared;

public static class Utils
{
    public static string GenerateAccessToken(int size = 64)
    {
        var tokenData = new byte[size];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(tokenData);
        }
        return Convert.ToBase64String(tokenData);
    }
    
    public static string GenerateRefreshToken(int size = 64)
    {
        var tokenData = new byte[size];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(tokenData);
        }
        return Convert.ToBase64String(tokenData);
    }

    public static string GenerateRandomId()
    {
        return Ulid.NewUlid().ToString();
    }
    
    public static string? GenerateS3PublicFileUrl(string bucketName, string? key)
    {
        // TODO: this is temp solutions https://github.com/torohealth/toro-ehr/issues/65
        return key == null ? null :
            $"https://pub-fe984b710fb84dc69650a7f1805b3f48.r2.dev/{key}";
    }

    private static (ZonedDateTime Start, ZonedDateTime End) GetTodayZoned(string tzId)
    {
        var tz = DateTimeZoneProviders.Tzdb[tzId];
        var clock = SystemClock.Instance;

        var now = clock.GetCurrentInstant();
        var todayLocal = now.InZone(tz).Date;

        return (tz.AtStartOfDay(todayLocal), tz.AtStartOfDay(todayLocal.PlusDays(1)));
    }

    public static (DateTime Start, DateTime End) GetTodayByTimeZone(string tzId)
    {
        var (startZ, endZ) = GetTodayZoned(tzId);
        return (startZ.ToDateTimeUtc(), endZ.ToDateTimeUtc());
    }
}