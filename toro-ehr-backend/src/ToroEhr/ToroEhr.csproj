<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Ardalis.SmartEnum" Version="8.2.0" />
        <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.400" />
        <PackageReference Include="AWSSDK.S3" Version="3.7.415.1" />
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
        <PackageReference Include="ClosedXML" Version="0.104.2" />
        <PackageReference Include="Coravel" Version="6.0.2" />
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="DotNetEnv" Version="3.1.1" />
        <PackageReference Include="FluentValidation" Version="11.11.0" />
        <PackageReference Include="Handlebars.Net" Version="2.1.6" />
        <PackageReference Include="MediatR" Version="12.4.1" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.1" />
        <PackageReference Include="NodaTime" Version="3.2.2" />
        <PackageReference Include="NSwag.AspNetCore" Version="14.2.0" />
        <PackageReference Include="RavenDB.Client" Version="7.0.0" />
        <PackageReference Include="Scrutor" Version="6.0.1" />
        <PackageReference Include="SendGrid" Version="9.29.3" />
        <PackageReference Include="Sentry.AspNetCore" Version="5.11.2" />
        <PackageReference Include="Sentry.Serilog" Version="5.11.2" />
        <PackageReference Include="Serilog" Version="4.2.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Slack.Webhooks" Version="1.1.5" />
        <PackageReference Include="Twilio" Version="7.9.1" />
        <PackageReference Include="Ulid" Version="1.3.4" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <None Update="Templates\Email\SetPassword\index.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Templates\Email\AppointmentConfirmed\index.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Templates\Email\EncounterMessage\index.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Templates\Email\AppointmentCanceled\index.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="IntegrationTests" />
    </ItemGroup>

</Project>
