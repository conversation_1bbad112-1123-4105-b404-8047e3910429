using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.NoteTemplate;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("note-templates")]
[Produces("application/json")]
public class NoteTemplateController : ControllerBase
{
    private readonly IMediator _mediator;

    public NoteTemplateController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List note templates
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<NoteTemplateResponse>>> ListNoteTemplates(
        [FromQuery] int? pageNumber, [FromQuery] int? pageSize, [FromQuery] string? searchParam,
        [FromQuery] string? specialtyFilter)
    {
        var result =
            await _mediator.Send(new ListNoteTemplatesQuery(new PagedSearchParams(pageNumber, pageSize, searchParam),
                specialtyFilter));
        return Ok(result);
    }
    
    /// <summary>
    ///     Get note template
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<NoteTemplateDetailsResponse>> GetNoteTemplate([FromRoute] string id)
    {
        var result = await _mediator.Send(new GetNoteTemplateQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     Create note template
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<string>> CreateNoteTemplate(
        [FromBody] CreateNoteTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Update note template
    /// </summary>
    /// <returns></returns>
    [HttpPut]
    public async Task<ActionResult<string>> UpdateNoteTemplate(
        [FromBody] UpdateNoteTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}