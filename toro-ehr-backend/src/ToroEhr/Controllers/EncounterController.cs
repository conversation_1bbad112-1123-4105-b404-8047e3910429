using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Encounter;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Features.Patient;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("encounter")]
[Produces("application/json")]
public class EncounterController : ControllerBase
{
    private readonly IMediator _mediator;

    public EncounterController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     Get encounter details
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<EncounterDetailsResponse>> GetEncounterById([FromRoute] string id)
    {
        var result = await _mediator.Send(new GetEncounterDetailsQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     List active encounters
    /// </summary>
    /// <returns></returns>
    [HttpGet("active")]
    public async Task<ActionResult<List<EncounterResponse>>> ListActiveEncounters()
    {
        var result = await _mediator.Send(new ListActiveEncountersQuery());
        return Ok(result);
    }

    /// <summary>
    ///     Get latest patient encounter
    /// </summary>
    /// <returns></returns>
    [HttpGet("latest/{patientId}")]
    public async Task<ActionResult<EncounterResponse?>> GetLatestPatientEncounter([FromRoute] string patientId)
    {
        var result = await _mediator.Send(new GetLatestPatientEncounterQuery(patientId));
        return Ok(result);
    }


    /// <summary>
    ///     Get active encounters
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<ActiveEncounterResponse>>> GetActiveEncounters(
        [FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam, bool showAll)
    {
        var result =
            await _mediator.Send(
                new BrowseActiveEncountersQuery(new PagedSearchParams(pageNumber, pageSize, searchParam), showAll));
        return Ok(result);
    }

    /// <summary>
    ///     Update scratch text for encounter
    /// </summary>
    /// <returns></returns>
    [HttpPost("scratch")]
    public async Task<ActionResult<Unit>> UpdateScratchText(UpdateScratchTextCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///    Add new vitals
    /// </summary>
    /// <returns></returns>
    [HttpPost("vitals")]
    public async Task<ActionResult<Unit>> AddVitalSigns(AddVitalSignsCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///    List vitals for
    /// </summary>
    /// <returns></returns>
    [HttpGet("vitals")]
    public async Task<ActionResult<VitalSignResponse>> ListVitalSigns(AddVitalSignsCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Get encounter questionnaires
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}/questionnaires")]
    public async Task<ActionResult<List<EncounterQuestionnaireResponse>>> GetEncounterQuestionnaires(string id)
    {
        var result = await _mediator.Send(new GetEncounterQuestionnairesQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     Mark encounter note completed
    /// </summary>
    /// <returns></returns>
    [HttpGet("notes/{id}")]
    public async Task<ActionResult<Unit>> MarkEncounterNoteCompleted(string id)
    {
        var result = await _mediator.Send(new MarkEncounterNoteCompletedCommand(id));
        return Ok(result);
    }

    /// <summary>
    ///     Create patient problem
    /// </summary>
    /// <returns></returns>
    [HttpPost("problems")]
    public async Task<ActionResult> CreatePatientProblem([FromBody] CreatePatientProblemCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Update encounter status
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("update-status")]
    public async Task<ActionResult<string>> UpdateStatus([FromBody] UpdateEncounterStatusCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Checkout encounter (mark as completed)
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("checkout")]
    public async Task<ActionResult<Unit>> CheckoutEncounter([FromBody] CheckoutEncounterCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Get communication for patient
    /// </summary>
    /// <returns></returns>
    [HttpGet("{encounterId}/communication")]
    public async Task<ActionResult<List<EncounterCommunicationMessageResponse>>> GetCommunications(string encounterId)
    {
        var result = await _mediator.Send(new GetEncounterCommunicationMessagesQuery(encounterId));
        return Ok(result);
    }

    /// <summary>
    ///     Send message or record call
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/communication")]
    public async Task<ActionResult<Unit>> SendEncounterMessage([FromForm] SendEncounterMessageCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Send message to practitioner
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/communication/reply")]
    public async Task<ActionResult<Unit>> SendMessageToPractitioner(
        [FromForm] SendEncounterMessageToPractitionerCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Mark message as seen
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/communication/seen")]
    public async Task<ActionResult<Unit>> MarkMessageAsSeen([FromForm] MarkMessageAsSeenCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Upload file for sending as link via sms
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/communication/upload")]
    public async Task<ActionResult<string>> UploadFileForEncounterCommunication(
        [FromForm] UploadFileForEncounterCommunicationCommand command,
        [FromRoute] string encounterId)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Search orders medicine, labs adn procedures
    /// </summary>
    /// <returns></returns>
    [HttpGet("{encounterId}/orders/seach")]
    public async Task<ActionResult<PaginatedList<SearchOrderEntryResponse>>> SearchOrderEntry(
        [FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam, [FromQuery] string type)
    {
        var result =
            await _mediator.Send(new SearchOrderEntryQuery(new PagedSearchParams(pageNumber, pageSize, searchParam), type));
        return Ok(result);
    }

    /// <summary>
    ///     Get orders
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/orders")]
    public async Task<ActionResult<List<OrderResponse>>> GetOrders(string encounterId)
    {
        var result = await _mediator.Send(new GetOrdersQuery(encounterId));
        return Ok(result);
    }

    /// <summary>
    ///     Create order medicine
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/order-medications")]
    public async Task<ActionResult<string>> CreateOrderMedication(CreateOrderMedicationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Edit order medicine
    /// </summary>
    /// <returns></returns>
    [HttpPut("{encounterId}/order-medications")]
    public async Task<ActionResult<Unit>> EditOrderMedication(EditOrderMedicationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Create order lab
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/order-labs")]
    public async Task<ActionResult<string>> CreateOrderLab(CreateOrderLabCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Edit order medicine
    /// </summary>
    /// <returns></returns>
    [HttpPut("{encounterId}/order-labs")]
    public async Task<ActionResult<Unit>> EditOrderLab(EditOrderLabCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Create order procedure
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/order-procedures")]
    public async Task<ActionResult<string>> CreateOrderProcedure(CreateOrderProcedureCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Edit order procedure
    /// </summary>
    /// <returns></returns>
    [HttpPut("{encounterId}/order-procedures")]
    public async Task<ActionResult<Unit>> EditOrderProcedure(EditOrderProcedureCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Create order immunization
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/order-immunization")]
    public async Task<ActionResult<string>> CreateOrderImmunization(CreateOrderImmunizationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Edit order immunization
    /// </summary>
    /// <returns></returns>
    [HttpPut("{encounterId}/order-immunization")]
    public async Task<ActionResult<Unit>> EditOrderImmunization(EditOrdeImmunizationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Create order bundle
    /// </summary>
    /// <returns></returns>
    [HttpPost("{encounterId}/order-bundle")]
    public async Task<ActionResult<string>> CreateOrderBundle(CreateOrderBundleCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Edit order bundle
    /// </summary>
    /// <returns></returns>
    [HttpPut("{encounterId}/order-bundle")]
    public async Task<ActionResult<Unit>> EditOrderBundle(EditOrderBundleCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Change order status
    /// </summary>
    /// <returns></returns>
    [HttpPut("{encounterId}/order/status")]
    public async Task<ActionResult<Unit>> ChangeOrderStatus(ChangeOrderStatusCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Save as bundle
    /// </summary>
    /// <returns></returns>
    [HttpPut("{encounterId}/order/save-as-bundle")]
    public async Task<ActionResult<Unit>> SaveOrdersAsBundle(SaveOrdersAsBundleCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Delete order
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{encounterId}/order")]
    public async Task<ActionResult<Unit>> DeleteOrder(DeleteOrderCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///    Get medication order record by order id
    /// </summary>
    /// <returns></returns>
    [HttpGet("{encounterId}/order-medications/{orderId}")]
    public async Task<ActionResult<MedicationRecordResponse>> GetMedicationRecordByOrderId(string encounterId,
        string orderId, string? bundleId)
    {
        var result = await _mediator.Send(new GetMedicationRecordByOrderIdQuery(encounterId, orderId, bundleId));
        return Ok(result);
    }


    /// <summary>
    ///     Change order timing status
    /// </summary>
    /// <returns></returns>
    [HttpPut("{encounterId}/order/timing-status")]
    public async Task<ActionResult<Unit>> ChangeOrderTimingStatus(ChangeOrderTimingStatusCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Change order note
    /// </summary>
    /// <returns></returns>
    [HttpPut("{encounterId}/order/note")]
    public async Task<ActionResult<Unit>> ChangeOrderNote(ChangeOrderNoteCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///    Charge encounter ipos
    /// </summary>
    /// <returns></returns>
    [HttpPost("billing/ipos")]
    public async Task<ActionResult<ProcessPaymentResponse>> ChargeEncounterIpos(ProcessPaymentIposCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///    Charge encounter ipos cloud
    /// </summary>
    /// <returns></returns>
    [HttpPost("billing/ipos-cloud")]
    public async Task<ActionResult<ProcessPaymentResponse>> ChargeEncounterIposCloud(
        ProcessPaymentIposCloudCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///    Browse encounters billing
    /// </summary>
    /// <returns></returns>
    [HttpGet("billing")]
    public async Task<ActionResult<PaginatedList<EncountersBillingResponse>>> BrowseEncountersBilling(
        [FromQuery] int? pageNumber, [FromQuery] int? pageSize, [FromQuery] string? searchParam,
        [FromQuery] string status)
    {
        var result =
            await _mediator.Send(
                new BrowseEncountersBillingQuery(new PagedSearchParams(pageNumber, pageSize, searchParam), status));
        return Ok(result);
    }

    /// <summary>
    ///    List encounters transactions
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}/transactions")]
    public async Task<ActionResult<List<EncounterTransactionResponse>>> ListEncounterTransactions(string id)
    {
        var result = await _mediator.Send(new ListEncounterTransactionsQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///    Void or refund transaction total
    /// </summary>
    /// <returns></returns>
    [HttpGet("transactions/{id}")]
    public async Task<ActionResult<List<ProcessPaymentResponse>>> VoidOrRefundTotal(string id)
    {
        var result = await _mediator.Send(new VoidOrRefundTotalCommand(id));
        return Ok(result);
    }

    /// <summary>
    ///    Custom refund with specific amount (always uses IPOS Cloud)
    /// </summary>
    /// <returns></returns>
    [HttpPost("transactions/{id}/custom-refund")]
    public async Task<ActionResult<ProcessPaymentResponse>> CustomRefund(CustomRefundCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///    List Encounter Layout
    /// </summary>
    /// <returns></returns>
    [HttpGet("layout")]
    public async Task<ActionResult<List<EncounterLayoutResponse>>> LoadEncounterLayout()
    {
        var result = await _mediator.Send(new LoadEncounterLayoutQuery());
        return Ok(result);
    }

    /// <summary>
    ///    Save encounter layout
    /// </summary>
    /// <returns></returns>
    [HttpPost("layout")]
    public async Task<ActionResult<Unit>> SaveEncounterLayout(SaveEncounterLayoutCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///    Delete encounter layout
    /// </summary>
    /// <returns></returns>
    [HttpDelete("layout")]
    public async Task<ActionResult<Unit>> DeleteEncounterLayout(DeleteEncounterLayoutCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}