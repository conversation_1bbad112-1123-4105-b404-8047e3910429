using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Questionnaire;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("questionnaires")]
[Produces("application/json")]
public class QuestionnaireController : ControllerBase
{
    private readonly IMediator _mediator;

    public QuestionnaireController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List questionnaires
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<QuestionnaireResponse>>> ListQuestionnaires(
        [FromQuery] int? pageNumber, [FromQuery] int? pageSize, [FromQuery] string? searchParam,
        [FromQuery] string locationFilter)
    {
        var result =
            await _mediator.Send(new BrowseQuestionnairesQuery(new PagedSearchParams(pageNumber, pageSize, searchParam),
                locationFilter));
        return Ok(result);
    }

    /// <summary>
    ///     Get questionnaire by id
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<QuestionnaireDetailsResponse>> GetQuestionnaire(string id)
    {
        var result = await _mediator.Send(new GetQuestionnaireQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     List questionnaires for patient
    /// </summary>
    /// <returns></returns>
    [HttpGet("patient")]
    public async Task<ActionResult<IEnumerable<PatientQuestionnaireResponse>>> ListPatientQuestionnaires(
        string placement, string? encounterId)
    {
        var result = await _mediator.Send(new ListPatientQuestionnairesQuery(placement, encounterId));
        return Ok(result);
    }

    /// <summary>
    ///     Creates a new questionnaire
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult> Create([FromBody] CreateQuestionnaireCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Update questionnaire
    /// </summary>
    /// <returns></returns>
    [HttpPut]
    public async Task<ActionResult> Update([FromBody] UpdateQuestionnaireCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Save questionnaire response
    /// </summary>
    /// <returns></returns>
    [HttpPost("response")]
    public async Task<ActionResult> SaveResponse([FromBody] SaveQuestionnaireResponseCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Deactivate questionnaire
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeactivateQuestionnaire(string id)
    {
        var result = await _mediator.Send(new DeactivateQuestionnaireCommand(id));
        return Ok(result);
    }
}