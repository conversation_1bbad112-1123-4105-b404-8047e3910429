using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Immunization;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace ToroEhr.Controllers;

[ApiController]
[Route("immunizations")]
[Produces("application/json")]
public class ImmunizationController : ControllerBase
{
    private readonly IMediator _mediator;

    public ImmunizationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List immunizations
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<ImmunizationResponse>>> ListImmunizations([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListImmunizationsQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Import immunizations.
    /// </summary>
    /// <returns></returns>
    [RequestSizeLimit(200_000_000)]
    [HttpPost]
    public async Task<ActionResult<string>> Import([FromForm] ImportImmunizationsCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}

