using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Employee;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("employees")]
[Produces("application/json")]
public class EmployeeController : ControllerBase
{
    private readonly IMediator _mediator;

    public EmployeeController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List employees
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<EmployeeResponse>>> ListEmployees([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new GetListEmployeesQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     List employees lookup
    /// </summary>
    /// <returns></returns>
    [HttpGet("lookup")]
    public async Task<ActionResult<List<SelectListItem>>> ListEmployeesLookup()
    {
        var result =
            await _mediator.Send(new GetListPractitionersLookupQuery());
        return Ok(result);
    }

    /// <summary>
    ///     List organization admins lookup
    /// </summary>
    /// <returns></returns>
    [HttpGet("organization-admin-lookup")]
    public async Task<ActionResult<List<SelectListItem>>> ListEmployeesAdminLookup(string organizationId)
    {
        var result =
            await _mediator.Send(new GetListOrganizationAdminsLookupQuery(organizationId));
        return Ok(result);
    }

    /// <summary>
    ///     Creates a new employee.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateEmployeeCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Update existing employee.
    /// </summary>
    /// <returns></returns>
    [HttpPut]
    public async Task<ActionResult<string>> Edit([FromBody] EditEmployeeCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Get location employee
    /// </summary>
    /// <returns></returns>
    [HttpGet("{employeeId}/locations/{locationId}")]
    public async Task<ActionResult<LocationEmployeeResponse>> GetLocationEmployee([FromRoute] string employeeId, 
        [FromRoute] string locationId)
    {
        var result =
            await _mediator.Send(new GetLocationEmployeeQuery(locationId, employeeId));
        return Ok(result);
    }

    /// <summary>
    ///     Update location employee general settings
    /// </summary>
    /// <returns></returns>
    [HttpPut("{employeeId}/locations/{locationId}/general-settings")]
    public async Task<ActionResult<Unit>> EditLocationEmployeeGeneralSettings([FromRoute] string employeeId,
        [FromRoute] string locationId, [FromBody] EditGeneralSettingsRequest request)
    {
        var result = await _mediator.Send(new EditLocationEmployeeGeneralSettingsCommand(locationId, employeeId, request));
        return Ok(result);
    }

    /// <summary>
    ///     Update location employee office hours
    /// </summary>
    /// <returns></returns>
    [HttpPut("{employeeId}/locations/{locationId}/office-hours")]
    public async Task<ActionResult<Unit>> EditOrganizationEmployeeOfficeHours([FromRoute] string employeeId,
        [FromRoute] string locationId, [FromBody] EditOfficeHoursRequest request)
    {
        var result = await _mediator.Send(new EditLocationEmployeeOfficeHoursCommand(locationId, employeeId, request));
        return Ok(result);
    }

    /// <summary>
    ///     Update location employee out of office hours
    /// </summary>
    /// <returns></returns>
    [HttpPut("{employeeId}/locations/{locationId}/out-of-office-hours")]
    public async Task<ActionResult<Unit>> EditLocationEmployeeOutOfOfficeHours([FromRoute] string employeeId, 
        [FromRoute] string locationId, [FromBody] EditOutOfOfficeHoursRequest request)
    {
        var result = await _mediator.Send(new EditLocationEmployeeOutOfOfficeHoursCommand(locationId, employeeId, request));
        return Ok(result);
    }

    /// <summary>
    ///     Get available appointment slots for organization employee
    /// </summary>
    /// <returns></returns>
    [HttpGet("{employeeId}/locations/{locationId}/appointment-slots")]
    public async Task<ActionResult<AvailableAppointmentSlotsResponse>> GetAvailableAppointmentSlots([FromRoute] string employeeId,
        [FromRoute] string locationId, DateTimeOffset? date, bool? reschedule)
    {
        var result =
            await _mediator.Send(new GetAvailableAppointmentSlotsQuery(employeeId, locationId, date ?? DateTimeOffset.UtcNow, reschedule ?? false));
        return Ok(result);
    }
}