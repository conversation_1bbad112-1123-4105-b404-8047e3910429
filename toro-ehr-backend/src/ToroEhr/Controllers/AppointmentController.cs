using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Appointment;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("appointments")]
[Produces("application/json")]
public class AppointmentController : ControllerBase
{
    private readonly IMediator _mediator;

    public AppointmentController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List appointments
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<AppointmentResponse>>> ListPatientAppointments(
        [FromQuery] int? pageNumber, [FromQuery] int? pageSize, [FromQuery] string? searchParam, [FromQuery] string? timeFilter)
    {
        var timeFilterEnum = !string.IsNullOrEmpty(timeFilter)
            ? Enums.AppointmentTimeFilter.FromName(timeFilter)
            : null;

        var result =
            await _mediator.Send(
                new ListPatientAppointmentsQuery(new PagedSearchParams(pageNumber, pageSize, searchParam), timeFilterEnum));
        return Ok(result);
    }
    
    /// <summary>
    ///     List employee appointments
    /// </summary>
    /// <returns></returns>
    [HttpGet("calendar-appointments")]
    public async Task<ActionResult<List<CalendarAppointmentResponse>>> ListCalendarAppointments(string? employeeId,
        DateTimeOffset? start, DateTimeOffset? end)
    {
        var result =
            await _mediator.Send(
                new ListCalendarAppointmentsQuery(employeeId, start, end));
        return Ok(result);
    }

    /// <summary>
    ///     Appointment by id
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<AppointmentDetailsResponse>> AppointmentById(string id)
    {
        var result = await _mediator.Send(new AppointmentByIdQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     Creates a new appointment for new patient.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("new-patient")]
    public async Task<ActionResult<string>> CreateForNewPatient([FromBody] CreateNewPatientAppointmentCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Creates a new appointment for existing patient.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("existing-patient")]
    public async Task<ActionResult<string>> CreateForExistingPatient(
        [FromBody] CreateExistingPatientAppointmentCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Edit appointment
    /// </summary>
    /// <returns></returns>
    [HttpPut]
    public async Task<ActionResult<string>> EditAppointment(
        [FromBody] EditAppointmentCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Cancel appointment
    /// </summary>
    /// <returns></returns>
    [HttpDelete]
    public async Task<ActionResult<string>> CancelAppointment(
        [FromBody] CancelAppointmentCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Mark appointment as checked in
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("mark-checked-in")]
    public async Task<ActionResult<string>> MarkAsCheckedIn([FromBody] MarkAppointmentAsCheckedInCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Confirm appointment
    /// </summary>
    /// <returns></returns>
    ///
    [HttpPost]
    [Route("confirm")]
    public async Task<ActionResult<string>> Confirm([FromBody] ConfirmAppointmentCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Check if patient has existing appointment on specified date
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Route("check-existing")]
    public async Task<ActionResult<ExistingAppointmentResponse?>> CheckExistingAppointment(
        [FromQuery] string patientId,
        [FromQuery] string employeeId,
        [FromQuery] DateTimeOffset date)
    {
        var result = await _mediator.Send(new CheckExistingAppointmentQuery(patientId, employeeId, date));
        return Ok(result);
    }
}