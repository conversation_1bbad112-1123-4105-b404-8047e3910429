using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.TextFormatting;

namespace ToroEhr.Controllers;

[ApiController]
[Route("text-formatting")]
[Produces("application/json")]
public class TextFormattingController : ControllerBase
{
    private readonly IMediator _mediator;

    public TextFormattingController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     Format text using AI to set proper punctuation, capitalization, and sentence structure
    /// </summary>
    /// <returns></returns>
    [HttpPost("format")]
    public async Task<ActionResult<FormatTextResponse>> FormatText([FromBody] FormatTextCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Transcribe audio recording to text using Whisper AI
    /// </summary>
    /// <returns></returns>
    [HttpPost("transcribe")]
    public async Task<ActionResult<TranscribeAudioResponse>> TranscribeAudio([FromForm] TranscribeAudioCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Transcribe audio recording and format the resulting text in one operation
    /// </summary>
    /// <returns></returns>
    [HttpPost("transcribe-and-format")]
    public async Task<ActionResult<TranscribeAndFormatAudioResponse>> TranscribeAndFormatAudio([FromForm] TranscribeAndFormatAudioCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}
