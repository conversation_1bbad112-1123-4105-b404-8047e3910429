using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;

namespace ToroEhr.Migrations;

/// <summary>
/// Migration to update existing questionnaires to use versioning system
/// This should be run once after deploying the versioning changes
/// </summary>
public static class MigrateQuestionnairesToVersioning
{
    public static async Task ExecuteAsync(IDocumentStore store)
    {
        using var session = store.OpenAsyncSession();
        
        // get all questionnaires that don't have versioning properties set
        var questionnaires = await session.Query<Questionnaire>()
            .ToListAsync();

        foreach (var questionnaire in questionnaires)
        {
            // set versioning properties for existing questionnaires
            if (string.IsNullOrEmpty(questionnaire.QuestionnaireId))
            {
                // use reflection to set private properties for migration
                var questionnaireIdProperty = typeof(Questionnaire).GetProperty("QuestionnaireId");
                questionnaireIdProperty?.SetValue(questionnaire, questionnaire.Id);
            }

            if (questionnaire.Version == 0)
            {
                var versionProperty = typeof(Questionnaire).GetProperty("Version");
                versionProperty?.SetValue(questionnaire, 1);
            }

            if (!questionnaire.IsLatest)
            {
                var isLatestProperty = typeof(Questionnaire).GetProperty("IsLatest");
                isLatestProperty?.SetValue(questionnaire, true);
            }

            if (questionnaire.CreatedAt == default)
            {
                var createdAtProperty = typeof(Questionnaire).GetProperty("CreatedAt");
                createdAtProperty?.SetValue(questionnaire, DateTime.UtcNow);
            }

            await session.StoreAsync(questionnaire);
        }

        // migrate questionnaire responses to include version
        var responses = await session.Query<QuestionnaireResponse>()
            .Where(x => x.QuestionnaireVersion == 0)
            .ToListAsync();

        foreach (var response in responses)
        {
            // set version to 1 for existing responses
            var versionProperty = typeof(QuestionnaireResponse).GetProperty("QuestionnaireVersion");
            versionProperty?.SetValue(response, 1);

            await session.StoreAsync(response);
        }

        await session.SaveChangesAsync();
    }
}
