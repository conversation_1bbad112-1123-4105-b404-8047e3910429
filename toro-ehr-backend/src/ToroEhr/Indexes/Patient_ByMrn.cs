using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Patient_ByMrn : AbstractIndexCreationTask<Patient, Patient_ByMrn.Entry>
{
    public class Entry
    {
        public string PatientId { get; set; } = null!;
        public string Mrn { get; set; } = null!;
        public string FirstName { get; set; } = null!;
        public string LastName { get; set; } = null!;
        public string Email { get; set; } = null!;
        public string[] SearchParams { get; set; } = null!;
    }

    public Patient_ByMrn()
    {
        Map = patients =>
            from patient in patients
            where patient.Mrn != null
            select new Entry
            {
                PatientId = patient.Id,
                Mrn = patient.Mrn,
                FirstName = patient.FirstName,
                LastName = patient.LastName,
                Email = patient.Email,
                SearchParams = new[]
                {
                    patient.Mrn,
                    patient.Email,
                    patient.FirstName,
                    patient.LastName,
                    patient.FullName
                }
            };

        Stores.Add(x => x.PatientId, FieldStorage.Yes);
        Stores.Add(x => x.Mrn, FieldStorage.Yes);
        Stores.Add(x => x.FirstName, FieldStorage.Yes);
        Stores.Add(x => x.LastName, FieldStorage.Yes);
        Stores.Add(x => x.Email, FieldStorage.Yes);
        Analyzers.Add(n => n.SearchParams, "LowerCaseWhitespaceAnalyzer");
    }
}
