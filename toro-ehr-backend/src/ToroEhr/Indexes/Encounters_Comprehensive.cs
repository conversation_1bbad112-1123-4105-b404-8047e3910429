using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Encounters_Comprehensive : AbstractMultiMapIndexCreationTask<Encounters_Comprehensive.Entry>
{
    public class Entry
    {
        // encounter core data
        public string EncounterId { get; set; } = null!;
        public string AppointmentId { get; set; } = null!;
        public string PatientId { get; set; } = null!;
        public string PractitionerId { get; set; } = null!;
        public string LocationId { get; set; } = null!;
        public string EncounterStatus { get; set; } = null!;
        public DateTimeOffset StartAt { get; set; }
        public DateTime StartAtDate { get; set; }  // computed field for date filtering
        public string PaymentStatus { get; set; } = null!;
        public DateTimeOffset? StartedAt { get; set; }
        public DateTimeOffset? CompletedAt { get; set; }

        // patient data (denormalized for performance)
        public string PatientMrn { get; set; } = null!;
        public string PatientFullName { get; set; } = null!;
        public string PatientEmail { get; set; } = null!;
        public DateTime PatientBirthday { get; set; }
        public string? PatientBirthSex { get; set; }
        public string? PatientPrimaryPhone { get; set; }
        public string? PatientAddress { get; set; }

        // practitioner data (denormalized)
        public string PractitionerFullName { get; set; } = null!;
        public string PractitionerShortName { get; set; } = null!;

        // location data (denormalized)
        public string LocationName { get; set; } = null!;

        // aggregated IDs for includes
        public List<string> NoteIds { get; set; } = new();
        public List<string> VitalSignIds { get; set; } = new();
        public List<string> MedicationIds { get; set; } = new();
        public List<string> QuestionnaireResponseIds { get; set; } = new();
    }

    public Encounters_Comprehensive()
    {
        AddMap<Appointment>(appointments =>
            from appointment in appointments
            where appointment.EncounterId != null
            let encounter = LoadDocument<Encounter>(appointment.EncounterId)
            let patient = LoadDocument<Patient>(encounter.PatientId)
            let practitioner = LoadDocument<Employee>(encounter.PractitionerId)
            let location = LoadDocument<Location>(encounter.LocationId)
            let currentAddress = patient.AddressHistory.LastOrDefault(x => x.ValidTo == null)
            select new Entry
            {
                EncounterId = encounter.Id,
                AppointmentId = appointment.Id,
                PatientId = encounter.PatientId,
                PractitionerId = encounter.PractitionerId,
                LocationId = encounter.LocationId,
                EncounterStatus = encounter.Status,
                StartAt = encounter.StartAt,
                StartAtDate = encounter.StartAt.Date,
                PaymentStatus = encounter.PaymentStatus,
                StartedAt = encounter.StartedAt,
                CompletedAt = encounter.CompletedAt,
                PatientMrn = patient.Mrn,
                PatientFullName = patient.FirstName + " " + patient.LastName,
                PatientEmail = patient.Email,
                PatientBirthday = patient.Birthday,
                PatientBirthSex = patient.BirthSex,
                PatientPrimaryPhone = patient.PhoneNumbers.FirstOrDefault(x => x.IsPrimary).Number,
                PatientAddress = currentAddress != null ?
                    (currentAddress.Address.Street + ", " + currentAddress.Address.City + ", " + currentAddress.Address.State + " " + currentAddress.Address.ZipCode).Trim() : null,
                PractitionerFullName = practitioner.FirstName + " " + practitioner.LastName,
                PractitionerShortName = practitioner.FirstName.Substring(0, 1) + ". " + practitioner.LastName,
                LocationName = location.Name,
                NoteIds = new List<string>(),
                VitalSignIds = new List<string>(),
                MedicationIds = new List<string>(),
                QuestionnaireResponseIds = new List<string>(),
            });

        AddMap<Note>(notes =>
            from note in notes
            where note.IsLatest
            let encounter = LoadDocument<Encounter>(note.EncounterId)
            let patient = LoadDocument<Patient>(encounter.PatientId)
            let practitioner = LoadDocument<Employee>(encounter.PractitionerId)
            let location = LoadDocument<Location>(encounter.LocationId)
            let currentAddress = patient.AddressHistory.LastOrDefault(x => x.ValidTo == null)
            select new Entry
            {
                EncounterId = note.EncounterId,
                AppointmentId = null!,
                PatientId = encounter.PatientId,
                PractitionerId = encounter.PractitionerId,
                LocationId = encounter.LocationId,
                EncounterStatus = encounter.Status,
                StartAt = encounter.StartAt,
                StartAtDate = encounter.StartAt.Date,
                PaymentStatus = encounter.PaymentStatus,
                StartedAt = encounter.StartedAt,
                CompletedAt = encounter.CompletedAt,
                PatientMrn = patient.Mrn,
                PatientFullName = patient.FirstName + " " + patient.LastName,
                PatientEmail = patient.Email,
                PatientBirthday = patient.Birthday,
                PatientBirthSex = patient.BirthSex,
                PatientPrimaryPhone = patient.PhoneNumbers.FirstOrDefault(x => x.IsPrimary).Number,
                PatientAddress = currentAddress != null ?
                    (currentAddress.Address.Street + ", " + currentAddress.Address.City + ", " + currentAddress.Address.State + " " + currentAddress.Address.ZipCode).Trim() : null,
                PractitionerFullName = practitioner.FirstName + " " + practitioner.LastName,
                PractitionerShortName = practitioner.FirstName.Substring(0, 1) + ". " + practitioner.LastName,
                LocationName = location.Name,
                NoteIds = new List<string> { note.Id },
                VitalSignIds = new List<string>(),
                MedicationIds = new List<string>(),
                QuestionnaireResponseIds = new List<string>(),
            });

        AddMap<VitalSign>(vitalSigns =>
            from vitalSign in vitalSigns
            let encounter = LoadDocument<Encounter>(vitalSign.EncounterId)
            let patient = LoadDocument<Patient>(encounter.PatientId)
            let practitioner = LoadDocument<Employee>(encounter.PractitionerId)
            let location = LoadDocument<Location>(encounter.LocationId)
            let currentAddress = patient.AddressHistory.LastOrDefault(x => x.ValidTo == null)
            select new Entry
            {
                EncounterId = vitalSign.EncounterId,
                AppointmentId = null!,
                PatientId = encounter.PatientId,
                PractitionerId = encounter.PractitionerId,
                LocationId = encounter.LocationId,
                EncounterStatus = encounter.Status,
                StartAt = encounter.StartAt,
                StartAtDate = encounter.StartAt.Date,
                PaymentStatus = encounter.PaymentStatus,
                StartedAt = encounter.StartedAt,
                CompletedAt = encounter.CompletedAt,
                PatientMrn = patient.Mrn,
                PatientFullName = patient.FirstName + " " + patient.LastName,
                PatientEmail = patient.Email,
                PatientBirthday = patient.Birthday,
                PatientBirthSex = patient.BirthSex,
                PatientPrimaryPhone = patient.PhoneNumbers.FirstOrDefault(x => x.IsPrimary).Number,
                PatientAddress = currentAddress != null ?
                    (currentAddress.Address.Street + ", " + currentAddress.Address.City + ", " + currentAddress.Address.State + " " + currentAddress.Address.ZipCode).Trim() : null,
                PractitionerFullName = practitioner.FirstName + " " + practitioner.LastName,
                PractitionerShortName = practitioner.FirstName.Substring(0, 1) + ". " + practitioner.LastName,
                LocationName = location.Name,
                NoteIds = new List<string>(),
                VitalSignIds = new List<string> { vitalSign.Id },
                MedicationIds = new List<string>(),
                QuestionnaireResponseIds = new List<string>(),
            });

        AddMap<OrderMedication>(medications =>
            from medication in medications
            let encounter = LoadDocument<Encounter>(medication.EncounterId)
            let patient = LoadDocument<Patient>(encounter.PatientId)
            let practitioner = LoadDocument<Employee>(encounter.PractitionerId)
            let location = LoadDocument<Location>(encounter.LocationId)
            let currentAddress = patient.AddressHistory.LastOrDefault(x => x.ValidTo == null)
            select new Entry
            {
                EncounterId = medication.EncounterId,
                AppointmentId = null!,
                PatientId = encounter.PatientId,
                PractitionerId = encounter.PractitionerId,
                LocationId = encounter.LocationId,
                EncounterStatus = encounter.Status,
                StartAt = encounter.StartAt,
                StartAtDate = encounter.StartAt.Date,
                PaymentStatus = encounter.PaymentStatus,
                StartedAt = encounter.StartedAt,
                CompletedAt = encounter.CompletedAt,
                PatientMrn = patient.Mrn,
                PatientFullName = patient.FirstName + " " + patient.LastName,
                PatientEmail = patient.Email,
                PatientBirthday = patient.Birthday,
                PatientBirthSex = patient.BirthSex,
                PatientPrimaryPhone = patient.PhoneNumbers.FirstOrDefault(x => x.IsPrimary).Number,
                PatientAddress = currentAddress != null ?
                    (currentAddress.Address.Street + ", " + currentAddress.Address.City + ", " + currentAddress.Address.State + " " + currentAddress.Address.ZipCode).Trim() : null,
                PractitionerFullName = practitioner.FirstName + " " + practitioner.LastName,
                PractitionerShortName = practitioner.FirstName.Substring(0, 1) + ". " + practitioner.LastName,
                LocationName = location.Name,
                NoteIds = new List<string>(),
                VitalSignIds = new List<string>(),
                MedicationIds = new List<string> { medication.Id },
                QuestionnaireResponseIds = new List<string>(),
            });

        AddMap<QuestionnaireResponse>(questionnaireResponses =>
            from questionnaireResponse in questionnaireResponses
            where questionnaireResponse.EncounterId != null
            let encounter = LoadDocument<Encounter>(questionnaireResponse.EncounterId)
            let patient = LoadDocument<Patient>(encounter.PatientId)
            let practitioner = LoadDocument<Employee>(encounter.PractitionerId)
            let location = LoadDocument<Location>(encounter.LocationId)
            let currentAddress = patient.AddressHistory.LastOrDefault(x => x.ValidTo == null)
            select new Entry
            {
                EncounterId = questionnaireResponse.EncounterId,
                AppointmentId = null!,
                PatientId = encounter.PatientId,
                PractitionerId = encounter.PractitionerId,
                LocationId = encounter.LocationId,
                EncounterStatus = encounter.Status,
                StartAt = encounter.StartAt,
                StartAtDate = encounter.StartAt.Date,
                PaymentStatus = encounter.PaymentStatus,
                StartedAt = encounter.StartedAt,
                CompletedAt = encounter.CompletedAt,
                PatientMrn = patient.Mrn,
                PatientFullName = patient.FirstName + " " + patient.LastName,
                PatientEmail = patient.Email,
                PatientBirthday = patient.Birthday,
                PatientBirthSex = patient.BirthSex,
                PatientPrimaryPhone = patient.PhoneNumbers.FirstOrDefault(x => x.IsPrimary).Number,
                PatientAddress = currentAddress != null ?
                    (currentAddress.Address.Street + ", " + currentAddress.Address.City + ", " + currentAddress.Address.State + " " + currentAddress.Address.ZipCode).Trim() : null,
                PractitionerFullName = practitioner.FirstName + " " + practitioner.LastName,
                PractitionerShortName = practitioner.FirstName.Substring(0, 1) + ". " + practitioner.LastName,
                LocationName = location.Name,
                NoteIds = new List<string>(),
                VitalSignIds = new List<string>(),
                MedicationIds = new List<string>(),
                QuestionnaireResponseIds = new List<string> { questionnaireResponse.Id },
            });

        Reduce = results =>
            from result in results
            group result by result.EncounterId into g
            let firstResult = g.First()
            select new Entry
            {
                EncounterId = g.Key,
                AppointmentId = firstResult.AppointmentId,
                PatientId = firstResult.PatientId,
                PractitionerId = firstResult.PractitionerId,
                LocationId = firstResult.LocationId,
                EncounterStatus = firstResult.EncounterStatus,
                StartAt = firstResult.StartAt,
                StartAtDate = firstResult.StartAtDate,
                PaymentStatus = firstResult.PaymentStatus,
                StartedAt = firstResult.StartedAt,
                CompletedAt = firstResult.CompletedAt,
                PatientMrn = firstResult.PatientMrn,
                PatientFullName = firstResult.PatientFullName,
                PatientEmail = firstResult.PatientEmail,
                PatientBirthday = firstResult.PatientBirthday,
                PatientBirthSex = firstResult.PatientBirthSex,
                PatientPrimaryPhone = firstResult.PatientPrimaryPhone,
                PatientAddress = firstResult.PatientAddress,
                PractitionerFullName = firstResult.PractitionerFullName,
                PractitionerShortName = firstResult.PractitionerShortName,
                LocationName = firstResult.LocationName,
                NoteIds = g.SelectMany(x => x.NoteIds).Distinct().ToList(),
                VitalSignIds = g.SelectMany(x => x.VitalSignIds).Distinct().ToList(),
                MedicationIds = g.SelectMany(x => x.MedicationIds).Distinct().ToList(),
                QuestionnaireResponseIds = g.SelectMany(x => x.QuestionnaireResponseIds).Distinct().ToList(),
            };

        Stores.Add(x => x.EncounterId, FieldStorage.Yes);
        Stores.Add(x => x.AppointmentId, FieldStorage.Yes);
        Stores.Add(x => x.PatientId, FieldStorage.Yes);
        Stores.Add(x => x.PractitionerId, FieldStorage.Yes);
        Stores.Add(x => x.LocationId, FieldStorage.Yes);
        Stores.Add(x => x.EncounterStatus, FieldStorage.Yes);
        Stores.Add(x => x.StartAt, FieldStorage.Yes);
        Stores.Add(x => x.StartAtDate, FieldStorage.Yes);
        Stores.Add(x => x.PaymentStatus, FieldStorage.Yes);
        Stores.Add(x => x.StartedAt, FieldStorage.Yes);
        Stores.Add(x => x.CompletedAt, FieldStorage.Yes);
        Stores.Add(x => x.PatientMrn, FieldStorage.Yes);
        Stores.Add(x => x.PatientFullName, FieldStorage.Yes);
        Stores.Add(x => x.PatientEmail, FieldStorage.Yes);
        Stores.Add(x => x.PatientBirthday, FieldStorage.Yes);
        Stores.Add(x => x.PatientBirthSex, FieldStorage.Yes);
        Stores.Add(x => x.PatientPrimaryPhone, FieldStorage.Yes);
        Stores.Add(x => x.PatientAddress, FieldStorage.Yes);
        Stores.Add(x => x.PractitionerFullName, FieldStorage.Yes);
        Stores.Add(x => x.PractitionerShortName, FieldStorage.Yes);
        Stores.Add(x => x.LocationName, FieldStorage.Yes);
        Stores.Add(x => x.NoteIds, FieldStorage.Yes);
        Stores.Add(x => x.VitalSignIds, FieldStorage.Yes);
        Stores.Add(x => x.MedicationIds, FieldStorage.Yes);
        Stores.Add(x => x.QuestionnaireResponseIds, FieldStorage.Yes);
    }
}
