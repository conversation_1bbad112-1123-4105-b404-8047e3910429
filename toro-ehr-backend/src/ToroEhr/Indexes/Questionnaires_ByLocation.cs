using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Questionnaires_ByLocation : AbstractIndexCreationTask<Questionnaire, Questionnaires_ByLocation.Entry>
{
    public class Entry
    {
        public string QuestionnaireId { get; set; } = null!;
        public string Title { get; set; } = null!;
        public string Classification { get; set; } = null!;
        public string Type { get; set; } = null!;
        public string Placement { get; set; } = null!;
        public string? OrganizationId { get; set; }
        public string? LocationId { get; set; }
        public string? LocationName { get; set; }
        public string? OrganizationName { get; set; }
        public bool IsActive { get; set; }
        public string[] SearchParams { get; set; } = null!;
    }

    public Questionnaires_ByLocation()
    {
        Map = questionnaires =>
            from questionnaire in questionnaires
            where questionnaire.IsLatest // only include latest versions
            let location = LoadDocument<Location>(questionnaire.LocationId)
            let organization = LoadDocument<Organization>(location != null ? location.OrganizationId : null)
            select new Entry
            {
                QuestionnaireId = questionnaire.Id,
                Title = questionnaire.Title,
                Classification = questionnaire.Classification,
                Type = questionnaire.Type,
                Placement = questionnaire.Placement,
                OrganizationId = organization != null ? organization.Id : null,
                LocationId = questionnaire.LocationId,
                LocationName = location != null ? location.Name : "General",
                OrganizationName = organization != null ? organization.Name : "General",
                IsActive = questionnaire.IsActive,
                SearchParams = new[]
                {
                    questionnaire.Title,
                    questionnaire.Classification,
                    questionnaire.Type,
                    location != null ? location.Name : "General",
                    organization != null ? organization.Name : "General",
                }
            };

        Stores.Add(x => x.QuestionnaireId, FieldStorage.Yes);
        Stores.Add(x => x.Title, FieldStorage.Yes);
        Stores.Add(x => x.Classification, FieldStorage.Yes);
        Stores.Add(x => x.Type, FieldStorage.Yes);
        Stores.Add(x => x.Placement, FieldStorage.Yes);
        Stores.Add(x => x.LocationName, FieldStorage.Yes);
        Stores.Add(x => x.OrganizationName, FieldStorage.Yes);
    }
}