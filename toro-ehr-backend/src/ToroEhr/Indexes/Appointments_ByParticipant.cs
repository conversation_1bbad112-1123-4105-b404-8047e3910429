using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Appointments_ByParticipant : AbstractIndexCreationTask<Appointment, Appointments_ByParticipant.Entry>
{
    public class Entry
    {
        public string AppointmentId { get; set; } = null!;
        public string EncounterId { get; set; } = null!;
        public string PatientId { get; set; } = null!;
        public string EmployeeId { get; set; } = null!;
        public string EmployeeFullName { get; set; } = null!;
        public string LocationName { get; set; } = null!;
        public string LocationAddress { get; set; } = null!;
        public DateTimeOffset? StartAt { get; set; }
        public string Status { get; set; } = null!;
        public int CheckInStartOffsetHours { get; set; }
        public decimal MissedAppointmentFee { get; set; }
        public string TimeZone { get; set; }
    }
    
    public Appointments_ByParticipant()
    {
        Map = appointments =>
            from appointment in appointments
            let employee = LoadDocument<Employee>(appointment.EmployeeId)
            let location = LoadDocument<Location>(appointment.LocationId)
            let locationEmployee = LoadDocument<LocationEmployee>($"{appointment.LocationId}/{appointment.EmployeeId}")
            select new Entry
            {
                AppointmentId = appointment.Id,
                EncounterId = appointment.EncounterId,
                PatientId = appointment.PatientId,
                EmployeeId = employee.Id,
                EmployeeFullName = $"{employee.FirstName} {employee.LastName}",
                LocationName = location.Name,
                LocationAddress = location.Address != null ?
                    string.Join(", ", new[] { location.Address.Street, location.Address.City, location.Address.State, location.Address.ZipCode }
                        .Where(s => !string.IsNullOrWhiteSpace(s))) : "",
                StartAt = appointment.StartAt,
                Status = appointment.Status,
                CheckInStartOffsetHours = location.CheckInStartOffsetHours,
                MissedAppointmentFee = location.MissedFee,
                TimeZone = locationEmployee.TimeZone
            };
        
        Stores.Add(x => x.AppointmentId, FieldStorage.Yes);
        Stores.Add(x => x.EncounterId, FieldStorage.Yes);
        Stores.Add(x => x.EmployeeFullName, FieldStorage.Yes);
        Stores.Add(x => x.LocationName, FieldStorage.Yes);
        Stores.Add(x => x.LocationAddress, FieldStorage.Yes);
        Stores.Add(x => x.Status, FieldStorage.Yes);
        Stores.Add(x => x.CheckInStartOffsetHours, FieldStorage.Yes);
        Stores.Add(x => x.MissedAppointmentFee, FieldStorage.Yes);
        Stores.Add(x => x.TimeZone, FieldStorage.Yes);
    }
}