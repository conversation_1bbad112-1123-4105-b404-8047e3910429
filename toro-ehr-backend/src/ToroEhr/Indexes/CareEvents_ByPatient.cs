using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class CareEvents_ByPatient : AbstractMultiMapIndexCreationTask<CareEvents_ByPatient.Entry>
{
    public class Entry
    {
        public string Id { get; set; } = null!;
        public string PatientId { get; set; } = null!;
        public string EventType { get; set; } = null!;
        public string? PractitionerId { get; set; }
        public string? EncounterId { get; set; }
        public string? LocationId { get; set; }
        public DateTimeOffset EventDate { get; set; }
        public string? Icd10Id { get; set; }

    }

    public CareEvents_ByPatient()
    {
        AddMap<Encounter>(encounters =>
            from encounter in encounters
            select new Entry
            {
                Id = encounter.Id,
                PatientId = encounter.PatientId,
                EventType = "Encounter",
                PractitionerId = encounter.PractitionerId,
                EncounterId = encounter.Id,
                LocationId = encounter.LocationId,
                EventDate = encounter.StartAt,
                Icd10Id = null
            });
        AddMap<PatientProblem>(patientProblems =>
            from patientProblem in patientProblems
            let encounter = LoadDocument<Encounter>(patientProblem.EncounterId)
            select new Entry
            {
                Id = patientProblem.Id,
                PatientId = patientProblem.PatientId,
                EventType = "Problem",
                PractitionerId = patientProblem.CreatedBy,
                EncounterId = encounter.Id,
                LocationId = encounter.LocationId,
                EventDate = patientProblem.CreatedAt,
                Icd10Id = patientProblem.Condition
            });
        AddMap<OrderMedication>(orderMedications =>
            from orderMed in orderMedications
            where orderMed.Status != "Draft"
            let encounter = LoadDocument<Encounter>(orderMed.EncounterId)
            select new Entry
            {
                Id = orderMed.Id,
                PatientId = orderMed.PatientId,
                EventType = "Medication",
                PractitionerId = orderMed.CreatedBy,
                EncounterId = encounter.Id,
                LocationId = encounter.LocationId,
                EventDate = orderMed.CreatedAt,
                Icd10Id = null
            });
        AddMap<OrderLab>(orderLabs =>
            from orderLab in orderLabs
            where orderLab.Status != "Draft"
            let encounter = LoadDocument<Encounter>(orderLab.EncounterId)
            select new Entry
            {
                Id = orderLab.Id,
                PatientId = orderLab.PatientId,
                EventType = "Lab",
                PractitionerId = orderLab.CreatedBy,
                EncounterId = encounter.Id,
                LocationId = encounter.LocationId,
                EventDate = orderLab.CreatedAt,
                Icd10Id = null
            });
        AddMap<OrderProcedure>(orderProcedures =>
            from orderProcedure in orderProcedures
            where orderProcedure.Status != "Draft"
            let encounter = LoadDocument<Encounter>(orderProcedure.EncounterId)
            select new Entry
            {
                Id = orderProcedure.Id,
                PatientId = orderProcedure.PatientId,
                EventType = "Procedure",
                PractitionerId = orderProcedure.CreatedBy,
                EncounterId = encounter.Id,
                LocationId = encounter.LocationId,
                EventDate = orderProcedure.CreatedAt,
                Icd10Id = null
            });
        AddMap<OrderBundle>(orderBundles =>
            from orderBundle in orderBundles
            where orderBundle.Status != "Draft"
            let encounter = LoadDocument<Encounter>(orderBundle.EncounterId)
            from order in orderBundle.Orders
            select new Entry
            {
                Id = orderBundle.Id,
                PatientId = orderBundle.PatientId,
                EventType = "Bundle",
                PractitionerId = orderBundle.CreatedBy,
                EncounterId = encounter.Id,
                LocationId = encounter.LocationId,
                EventDate = orderBundle.CreatedAt,
                Icd10Id = null
            });

        Stores.Add(x => x.Id, FieldStorage.Yes);
        Stores.Add(x => x.PatientId, FieldStorage.Yes);
        Stores.Add(x => x.EventType, FieldStorage.Yes);
        Stores.Add(x => x.EncounterId, FieldStorage.Yes);
        Stores.Add(x => x.LocationId, FieldStorage.Yes);
        Stores.Add(x => x.PractitionerId, FieldStorage.Yes);
        Stores.Add(x => x.EventDate, FieldStorage.Yes);
        Stores.Add(x => x.Icd10Id, FieldStorage.Yes);
    }
}