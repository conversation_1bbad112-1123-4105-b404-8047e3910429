using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class ContactPerson_ByOrganization : AbstractMultiMapIndexCreationTask<ContactPerson_ByOrganization.Entry>
{
    public class Entry
    {
        public string? EmployeeId { get; set; }

        public string OrganizationId { get; set; } = null!;

        public string DefaultLocationId { get; set; } = null!;

        public string[] SearchParams { get; set; } = null!;
    }

    public ContactPerson_ByOrganization()
    {
        //AddMap<LocationEmployee>(locationEmployees =>
        //from locationEmployee in locationEmployees
        //where locationEmployee.EmployeeRoles.Contains("OrganizationAdmin") && locationEmployee.IsOrganizationContact
        //let organization = LoadDocument<Organization>(locationEmployee.OrganizationId)
        //select new Entry
        //{
        //    EmployeeId = locationEmployee.EmployeeId,
        //    LocationEmployeeId = locationEmployee.Id,
        //    OrganizationId = locationEmployee.OrganizationId,
        //    DefaultLocationId = null!,
        //    SearchParams = new[]
        //        {
        //            organization.Name
        //        }
        //});
        AddMap<Organization>(organizations =>
        from organization in organizations
        select new Entry
        {
            EmployeeId = organization.ContactEmployee,
            OrganizationId = organization.Id,
            DefaultLocationId = null!,
            SearchParams = new[]
                {
                            organization.Name
                }
        });
        AddMap<Location>(locations =>
        from location in locations
        where location.IsDefault
        let organization = LoadDocument<Organization>(location.OrganizationId)
        select new Entry
        {
            EmployeeId = null!,
            OrganizationId = location.OrganizationId,
            DefaultLocationId = location.Id,
            SearchParams = new[]
                {
                            organization.Name
                }
        });

        Reduce = results => from result in results
                            group result by new { result.OrganizationId, result.SearchParams }
            into g
                            select new Entry
                            {
                                EmployeeId = g.First(x => x.EmployeeId != null).EmployeeId,
                                OrganizationId = g.Key.OrganizationId,
                                DefaultLocationId = g.First(x => x.DefaultLocationId != null).DefaultLocationId,
                                SearchParams = g.Key.SearchParams
                            };

        Stores.Add(x => x.EmployeeId, FieldStorage.Yes);
        Stores.Add(x => x.OrganizationId, FieldStorage.Yes);
        Stores.Add(x => x.DefaultLocationId, FieldStorage.Yes);
        Analyzers.Add(n => n.SearchParams, "LowerCaseWhitespaceAnalyzer");
    }
}
