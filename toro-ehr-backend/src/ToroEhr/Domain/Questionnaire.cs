using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class Questionnaire : Entity
{
    [JsonConstructor]
    private Questionnaire(string? locationId, string title, string classification, string placement, List<Question> questions, DateTime createdAt)
    {
        QuestionnaireId = Id;
        Version = 1;
        IsLatest = true;
        LocationId = locationId;
        Title = title;
        Classification = classification;
        Placement = placement;
        Questions = questions;
        IsActive = true;
        CreatedAt = createdAt;
    }

    public string QuestionnaireId { get; private set; }
    public int Version { get; private set; }
    public bool IsLatest { get; private set; }
    public string? LocationId { get; private set; }
    public string Title { get; private set; }
    public string Placement { get; private set; }
    public string Classification { get; private set; }
    public List<Question> Questions { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }

    public static Questionnaire Create(string? locationId, string title, string type, string placement,
        List<Question> questions, DateTime createdAt)
    {
        return new Questionnaire(locationId, title, type, placement, questions, createdAt);
    }

    public void MarkAsNotLatest()
    {
        IsLatest = false;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public static Questionnaire CreateNewVersion(Questionnaire previousQuestionnaire, string title, string classification,
        string placement, List<Question> questions, DateTime createdAt)
    {
        return new Questionnaire(
            previousQuestionnaire.LocationId,
            title,
            classification,
            placement,
            questions,
            createdAt
        )
        {
            QuestionnaireId = previousQuestionnaire.QuestionnaireId,
            Version = previousQuestionnaire.Version + 1,
            IsLatest = true
        };
    }

    public bool HasRequiredQuestionsChanged(List<Question> newQuestions)
    {
        var currentRequiredQuestions = Questions.Where(q => q.IsRequired).ToList();
        var newRequiredQuestions = newQuestions.Where(q => q.IsRequired).ToList();

        // check if any required questions were added
        var addedRequiredQuestions = newRequiredQuestions
            .Where(nq => !currentRequiredQuestions.Any(cq => cq.Id == nq.Id))
            .ToList();

        // check if any existing questions became required
        var becameRequiredQuestions = newRequiredQuestions
            .Where(nq => Questions.Any(cq => cq.Id == nq.Id && !cq.IsRequired))
            .ToList();

        // check if any required questions had their text modified
        var modifiedRequiredQuestions = newRequiredQuestions
            .Where(nq => currentRequiredQuestions.Any(cq => cq.Id == nq.Id &&
                (cq.Text != nq.Text || cq.Type != nq.Type || !cq.Options.SequenceEqual(nq.Options))))
            .ToList();

        return addedRequiredQuestions.Any() || becameRequiredQuestions.Any() || modifiedRequiredQuestions.Any();
    }
}

public record Question(string Id, string Text, string Type, bool IsRequired = false, IEnumerable<string> Options = null!)
{
    public IEnumerable<string> Options { get; init; } = Options ?? [];
}