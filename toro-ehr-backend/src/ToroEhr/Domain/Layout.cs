using Newtonsoft.Json;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public class Layout : Entity
{
    [JsonConstructor]
    private Layout(string userId, string name, List<EncounterBox> encounterBoxes, decimal containerHeight, decimal containerWidth)
    {
        UserId = userId;
        Name = name;
        EncounterBoxes = encounterBoxes;
        ContainerHeight = containerHeight;
        ContainerWidth = containerWidth;
    }

    public string UserId { get; private set; }
    public string Name { get; private set; }
    public List<EncounterBox> EncounterBoxes { get; private set; }
    public decimal ContainerHeight { get; private set; }
    public decimal ContainerWidth { get; private set; }

    public static Layout Create(string userId, string name, List<EncounterBox> encounterBoxes, decimal containerHeight, decimal containerWidth)
    {
        return new Layout(userId, name, encounterBoxes, containerHeight, containerWidth);
    }
}
