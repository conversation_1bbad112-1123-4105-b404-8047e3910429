using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class Employee : Entity
{
    [JsonConstructor]
    private Employee(string email, string firstName, string lastName, string npi,
        string phoneNumber, Address? address)
    {
        Email = email;
        FirstName = firstName;
        LastName = lastName;
        Npi = npi;
        PhoneNumber = phoneNumber;
        Address = address;
    }

    public string Email { get; private set; }

    public string FirstName { get; private set; }

    public string LastName { get; private set; }

    public string Npi { get; private set; }

    public string PhoneNumber { get; private set; }

    public Address? Address { get; private set; }

    [JsonIgnore]
    public string ShortName => $"{FirstName[0]}. {LastName}";
    
    [JsonIgnore]
    public string FullName => $"{FirstName} {LastName}";

    public void Update(string email, string firstName, string lastName, string npi,
        string phoneNumber, Address address)
    {
        Email = email;
        FirstName = firstName;
        LastName = lastName;
        Npi = npi;
        PhoneNumber = phoneNumber;
        Address = address;
    }

    public static Employee Create(string email, string firstName, string lastName, string npi,
        string phoneNumber, Address? address)
    {
        return new Employee(email, firstName, lastName, npi, phoneNumber, address);
    }
}