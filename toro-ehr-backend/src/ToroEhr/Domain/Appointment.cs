using Newtonsoft.Json;
using ToroEhr.Enums;
using ToroEhr.Features.Appointment;

namespace ToroEhr.Domain;

public sealed class Appointment : Entity
{
    [JsonConstructor]
    private Appointment(string patientId, string employeeId, string locationId, string? encounterId,
        DateTimeOffset startAt, DateTimeOffset endAt, int durationInMinutes, string status)
    {
        PatientId = patientId;
        EmployeeId = employeeId;
        LocationId = locationId;
        EncounterId = encounterId;
        StartAt = startAt;
        EndAt = endAt;
        DurationInMinutes = durationInMinutes;
        Status = status;
    }

    public string PatientId { get; private set; }

    public string EmployeeId { get; private set; }

    public string LocationId { get; private set; }

    public string? EncounterId { get; private set; }

    public DateTimeOffset StartAt { get; private set; }

    public DateTimeOffset EndAt { get; private set; }

    public int DurationInMinutes { get; private set; }

    public string Status { get; private set; }

    public static Appointment Create(string patientId, string employeeId, string locationId, string? encounterId,
        DateTimeOffset startAt, DateTimeOffset endAt, int durationInMinutes, AppointmentStatus status) => 
        new(patientId, employeeId, locationId, encounterId, startAt, endAt, durationInMinutes, status.Name);

    public void Update(EditAppointmentCommand command)
    {
        EmployeeId = command.EmployeeId;
        LocationId = command.LocationId;
        StartAt = command.StartAt;
        EndAt = command.StartAt.AddMinutes(command.DurationInMinutes);
        DurationInMinutes = command.DurationInMinutes;
    }

    public void Confirm(string encounterId)
    {
        Status = AppointmentStatus.Confirmed.Name;
        EncounterId = encounterId;
    }

    public void CheckIn()
    {
        Status = AppointmentStatus.CheckedIn.Name;
    }

    public void Cancel()
    {
        Status = AppointmentStatus.Canceled.Name;
    }

    public void MarkAsCanceledLate()
    {
        Status = AppointmentStatus.CanceledLate.Name;
    }

    public void MarkAsMissed()
    {
        Status = AppointmentStatus.Missed.Name;
    }

    public void MarkAsCompleted()
    {
        Status = AppointmentStatus.Completed.Name;
    }

    public bool IsConfirmed()
    {
        return Status == AppointmentStatus.Confirmed.Name;
    }

    public void MarkAsInProgress()
    {
        Status = AppointmentStatus.InProgress.Name;
    }

    public bool IsWithinCheckInWindow(DateTimeOffset currentTime, int checkInStartOffsetHours)
    {
        var checkInWindowStart = StartAt.AddHours(-checkInStartOffsetHours);
        return currentTime >= checkInWindowStart;
    }
}