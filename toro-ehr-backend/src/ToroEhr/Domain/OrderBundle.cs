using Newtonsoft.Json;
using ToroEhr.Enums;

namespace ToroEhr.Domain;

public class OrderBundle : Order
{
    [JsonConstructor]
    private OrderBundle(string encounterId, string patientId, string? bundleTemplateId, string name, string priority,
        string status, List<Order> orders, DateTimeOffset createdAt, string createdBy) :
    base(encounterId, patientId, name, priority, status, createdAt, createdBy)
    {
        BundleTemplateId = bundleTemplateId;
        Orders = orders;
    }

    public string? BundleTemplateId { get; private set; }
    public List<Order> Orders { get; private set; }

    public static OrderBundle Create(
    string encounterId, string patientId, string? bundleTemplateId, string name, string priority, List<Order> orders, DateTimeOffset createdAt, string createdBy)
    {
        return new OrderBundle(encounterId, patientId, bundleTemplateId, name, priority, OrderStatus.Draft,
            orders, createdAt, createdBy);
    }
}
