namespace ToroEhr.Domain;

public sealed class PatientProblem : Entity
{
    public PatientProblem(string patientId, string? encounterId, string condition, string clinicalConditionStatus, string conditionVerificationStatus,
        DateOnly diagnosisDate, DateOnly? abatement, DateTimeOffset cratedAt, string createdBy)
    {
        PatientId = patientId;
        EncounterId = encounterId;
        Condition = condition;
        ClinicalConditionStatus = clinicalConditionStatus;
        ConditionVerificationStatus = conditionVerificationStatus;
        DiagnosisDate = diagnosisDate;
        Abatement = abatement;
        CreatedAt = cratedAt;
        CreatedBy = createdBy;
    }

    public string PatientId { get; private set; }

    public string? EncounterId { get; private set; }

    public string Condition { get; private set; }

    public string ClinicalConditionStatus { get; private set; }

    public string ConditionVerificationStatus { get; private set; }

    public DateOnly DiagnosisDate { get; private set; }

    public DateOnly? Abatement { get; private set; }

    public DateTimeOffset CreatedAt { get; private set; }

    public string CreatedBy { get; private set; }

    public static PatientProblem Create(string patientId, string? encounterId, string condition, string clinicalConditionStatus, string conditionVerificationStatus,
        DateOnly diagnosisDate, DateOnly? abatement, string createdBy)
    {
        return new PatientProblem(patientId, encounterId, condition, clinicalConditionStatus, conditionVerificationStatus, diagnosisDate, abatement, DateTimeOffset.UtcNow, createdBy);
    }
}