using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class Immunization : CodingEntity
{
    [JsonConstructor]
    private Immunization(string code, string codeSystem, string codeSystemName, string codeSystemVersion, string displayName,
        string shortDescription, string note, bool nonVaccine, string status) :
        base(code, codeSystem, codeSystemName, codeSystemVersion, displayName)
    {
        ShortDescription = shortDescription;
        Note = note;
        NonVaccine = nonVaccine;
        Status = status;
    }

    public string ShortDescription { get; private set; }
    public string Note { get; private set; }
    public bool NonVaccine { get; private set; }
    public string Status { get; private set; }
    public bool ShowToPatient => DisplayName.Contains("influenza") || DisplayName.Contains("pneumococcal");

    public static Immunization Create(string code, string codeSystem, string codeSystemName, string codeSystemVersion, 
        string displayName, string shortDescription, string note, bool nonVaccine, string status)
    {
        return new Immunization(code, codeSystem, codeSystemName, codeSystemVersion, displayName, shortDescription,
            note, nonVaccine, status);
    }
}