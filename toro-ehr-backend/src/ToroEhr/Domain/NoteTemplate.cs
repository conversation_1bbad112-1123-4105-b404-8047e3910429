using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class NoteTemplate : Entity
{
    [JsonConstructor]
    private NoteTemplate(string name, string classification, string? specialization,
        string specialityCode, string documentType, List<NoteTemplateField> fields, string organizationId,
        List<string> locationIds, string creatorId)
    {
        Name = name;
        Classification = classification;
        Specialization = specialization;
        DocumentType = documentType;
        SpecialityCode = specialityCode;
        Fields = fields;
        OrganizationId = organizationId;
        LocationIds = locationIds;
        CreatorId = creatorId;
    }

    public string Name { get; private set; }

    public string Classification { get; private set; }

    public string? Specialization { get; private set; }

    public string SpecialityCode { get; private set; }

    public string DocumentType { get; private set; }

    public List<NoteTemplateField> Fields { get; private set; }

    public string OrganizationId { get; private set; }

    public List<string> LocationIds { get; private set; }

    public string CreatorId { get; private set; }

    public static NoteTemplate Create(string name, string classification, string? specialization,
        string documentType, string specialityCode, List<NoteTemplateField> fields, string organizationId,
        List<string> locationIds, string creatorId)
    {
        return new NoteTemplate(name, classification, specialization, documentType, specialityCode, fields,
            organizationId, locationIds, creatorId);
    }

    public void Update(string name, string classification, string? specialization,
        string documentType, string specialityCode, List<NoteTemplateField> fields, List<string> locationIds)
    {
        Name = name;
        Classification = classification;
        Specialization = specialization;
        DocumentType = documentType;
        SpecialityCode = specialityCode;
        Fields = fields;
        LocationIds = locationIds;
    }
}

public record NoteTemplateField(string Name, string Value, bool IsRequired);