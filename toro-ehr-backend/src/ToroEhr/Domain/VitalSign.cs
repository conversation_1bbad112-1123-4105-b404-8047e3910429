using Newtonsoft.Json;

namespace ToroEhr.Domain;

public class VitalSign : AuditableEntity
{
    [JsonConstructor]

    private VitalSign(string patient, string encounterId, string type, string value, 
        string status, DateTimeOffset? recordedDate, string? recordedBy, DateTimeOffset createdAt, string createdBy)
        : base(createdAt, createdBy)
    {
        PatientId = patient;
        EncounterId = encounterId;
        Type = type;
        Value = value;
        Status = status;
        RecordedDate = recordedDate;
        RecordedBy = recordedBy;
    }

    public string PatientId { get; private set; }

    public string EncounterId { get; private set; }

    public string Type { get; private set; }

    public string Value { get; private set; }

    public string Status { get; private set; }

    public DateTimeOffset? RecordedDate { get; private set; }

    public string? RecordedBy { get; private set; }

    public long? RecordedDateTimestamp => RecordedDate?.ToUnixTimeSeconds();

    public static VitalSign Create(string patientId, string encounterId,
        string type, string value, string status, DateTimeOffset? recordedDate, string? recordedBy, DateTimeOffset createdAt, string createdBy) => 
        new(patientId, encounterId, type, value, status, recordedDate, recordedBy, createdAt, createdBy);

    public void Update(string value, string status, DateTimeOffset? recordedDate, string? recordedBy, DateTimeOffset modifiedAt, string modifiedBy)
    {
        Value = value; 
        Status = status;
        RecordedDate = recordedDate;
        RecordedBy = recordedBy;
        SetModified(modifiedAt, modifiedBy);
    }
}