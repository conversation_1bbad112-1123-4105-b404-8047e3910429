using Newtonsoft.Json;
using ToroEhr.Features.Questionnaire;

namespace ToroEhr.Domain;

public sealed class QuestionnaireResponse : Entity
{
    [JsonConstructor]
    private QuestionnaireResponse(string patientId, string questionnaireId, int questionnaireVersion, string? encounterId, DateTime submittedAt,
        List<Answer> answers)
    {
        PatientId = patientId;
        QuestionnaireId = questionnaireId;
        QuestionnaireVersion = questionnaireVersion;
        EncounterId = encounterId;
        SubmittedAt = submittedAt;
        Answers = answers;
    }

    public string PatientId { get; private set; }

    public string QuestionnaireId { get; private set; }

    public int QuestionnaireVersion { get; private set; }

    public string? EncounterId { get; private set; }

    public DateTime SubmittedAt { get; private set; }

    public List<Answer> Answers { get; private set; }

    public static QuestionnaireResponse Create(string patientId, string questionnaireId, int questionnaireVersion, string? encounterId,
        DateTime submittedAt, List<Answer> answers) =>
        new(patientId, questionnaireId, questionnaireVersion, encounterId, submittedAt, answers);

    public void Update(IEnumerable<QuestionAnswer> answers, DateTime submittedAt)
    {
        Answers = answers.Select(x => new Answer(x.QuestionId, x.Answers)).ToList();
        SubmittedAt = submittedAt;
    }

    public void UpdateToNewVersion(int newVersion, IEnumerable<QuestionAnswer> answers, DateTime submittedAt)
    {
        QuestionnaireVersion = newVersion;
        Answers = answers.Select(x => new Answer(x.QuestionId, x.Answers)).ToList();
        SubmittedAt = submittedAt;
    }
}

public sealed record Answer(string QuestionId, List<string> Values);