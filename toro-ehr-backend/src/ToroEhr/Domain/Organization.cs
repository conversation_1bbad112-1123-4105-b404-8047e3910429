using FluentValidation;
using Newtonsoft.Json;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.ErrorHandling;

namespace ToroEhr.Domain;

public sealed class Organization : Entity
{
    [JsonConstructor]
    private Organization(string name, string contactEmployee, string type)
    {
        Name = name;
        ContactEmployee = contactEmployee;
        Type = type;
    }

    public string Name { get; private set; }
    public string ContactEmployee { get; private set; }
    public string Type { get; private set; }

    private static readonly OrganizationValidator OrgValidator = new();

    public static Organization Create(string name, string contactEmployee, string type)
    {
        Organization org = new Organization(name, contactEmployee, type);
        return Validator.Validate(org, OrgValidator);
    }

    public void Update(string name, string contactEmpoyee, string type) 
    {
        Name = name;
        ContactEmployee = contactEmpoyee;
        Type = type;
        
        Validator.Validate(this, OrgValidator);
    }
}

public class OrganizationValidator : AbstractValidator<Organization>
{
    public OrganizationValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Type)
            .Must(s => OrganizationType.TryFromName(s, out _))
            .WithMessage(x => $"Organization Type '{x}' not supported");
    }
}