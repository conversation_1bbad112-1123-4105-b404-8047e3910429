using Newtonsoft.Json;
using ToroEhr.Enums;

namespace ToroEhr.Domain;

public sealed class Notification : Entity
{
    [JsonConstructor]
    private Notification(string userId, string userType, string notificationType, string status, string title, 
        string message, string? relatedEntityId, string? relatedEntityType, DateTime createdAt)
    {
        UserId = userId;
        UserType = userType;
        NotificationType = notificationType;
        Status = status;
        Title = title;
        Message = message;
        RelatedEntityId = relatedEntityId;
        RelatedEntityType = relatedEntityType;
        CreatedAt = createdAt;
    }

    public string UserId { get; private set; }
    public string UserType { get; private set; } // "Patient" or "Employee"
    public string NotificationType { get; private set; }
    public string Status { get; private set; }
    public string Title { get; private set; }
    public string Message { get; private set; }
    public string? RelatedEntityId { get; private set; } // appointmentId, encounterId, etc.
    public string? RelatedEntityType { get; private set; } // "Appointment", "Encounter", etc.
    public DateTime CreatedAt { get; private set; }
    public DateTime? ReadAt { get; private set; }

    public void MarkAsRead(DateTime readAt)
    {
        Status = NotificationStatus.Read.Name;
        ReadAt = readAt;
    }

    public static Notification Create(string userId, string userType, string notificationType, string title, 
        string message, string? relatedEntityId, string? relatedEntityType, DateTime createdAt)
    {
        return new Notification(userId, userType, notificationType, NotificationStatus.Unread.Name, title, 
            message, relatedEntityId, relatedEntityType, createdAt);
    }
}
