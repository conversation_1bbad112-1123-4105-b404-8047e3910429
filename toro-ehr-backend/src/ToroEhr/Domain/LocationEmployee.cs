using Newtonsoft.Json;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public sealed class LocationEmployee
{
    [JsonConstructor]
    private LocationEmployee(string organizationId, string locationId, string employeeId, List<string> employeeRoles,
        string calendarColor, string timeZone, string invitationToken, DateTime invitedAt, bool pendingApprovalAppointments, string? specialtyClassification)
    {
        Id = $"{locationId}/{employeeId}";
        OrganizationId = organizationId;
        LocationId = locationId;
        EmployeeId = employeeId;
        EmployeeRoles = employeeRoles;
        CalendarColor = calendarColor;
        TimeZone = timeZone;
        InvitationToken = invitationToken;
        InvitedAt = invitedAt;
        PendingApprovalAppointments = pendingApprovalAppointments;
        SpecialtyClassification = specialtyClassification;
    }

    public string Id { get; private set; }

    public string OrganizationId { get; private set; }

    public string LocationId { get; private set; }

    public string EmployeeId { get; private set; }

    public List<string> EmployeeRoles { get; private set; }
    
    public DateTime InvitedAt { get; private set; }

    public string? InvitationToken { get; private set; }

    public DateTime? InviteAcceptedAt { get; private set; }

    public int AppointmentDurationInMinutes { get; private set; }

    public bool PendingApprovalAppointments { get; private set; }

    public string CalendarColor { get; private set; }

    public string TimeZone { get; private set; }

    public int NumberOfAppointmentOverlaps { get; private set; }

    public List<string> ReceivedNotificationPreferences { get; private set; } = [];

    public List<OfficeHours> OfficeHours { get; private set; } = [];

    public List<OutOfOfficeHours> OutOfOfficeHours { get; private set; } = [];

    public string? SpecialtyClassification { get; private set; }


    public void AcceptInvitation(DateTime now)
    {
        InviteAcceptedAt = now;
        InvitationToken = null;
    }
    
    public void UpdateGeneralSettings(string calendarColor, int numberOfAppointmentOverlaps, 
        int appointmentDurationInMinutes, bool pendingApprovalAppointments, List<string> receivedNotificationPreferences, 
        string? specialtyClassification)
    {
        NumberOfAppointmentOverlaps = numberOfAppointmentOverlaps;
        PendingApprovalAppointments = pendingApprovalAppointments;
        CalendarColor = calendarColor;
        AppointmentDurationInMinutes = appointmentDurationInMinutes;
        ReceivedNotificationPreferences = receivedNotificationPreferences;
        SpecialtyClassification = specialtyClassification;
    }

    public void UpdateOfficeHours(List<OfficeHours> officeHours)
    {
        OfficeHours = officeHours;
    }

    public void UpdateOutOfOfficeHours(List<OutOfOfficeHours> outOfOfficeHours)
    {
        OutOfOfficeHours = outOfOfficeHours;
    }

    public void UpdateTimeZone(string timeZone)
    {
        TimeZone = timeZone;
    }

    public void Update(string timeZone, List<string> employeeRoles, string calendarColor)
    {
        TimeZone = timeZone;
        EmployeeRoles = employeeRoles;
        CalendarColor = calendarColor;
    }

    public static LocationEmployee Create(string organizationId, string locationId, string employeeId, List<string> employeeRoles,
        string calendarColor, string timeZone, string invitationToken, DateTime invitedAt, bool pendingApprovalAppointments, string? specialtyClassification)
    { 
        return new(organizationId, locationId, employeeId, employeeRoles, calendarColor, timeZone, invitationToken, invitedAt, pendingApprovalAppointments,
            specialtyClassification);
    }        
}