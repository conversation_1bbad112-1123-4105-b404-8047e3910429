using Newtonsoft.Json;
using ToroEhr.Features.Patient;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public sealed class Patient : Entity
{
    [JsonConstructor]
    private Patient(string mrn, string email, string firstName, string lastName, DateTime birthday, string phoneNumber,
        DateTime timestamp)
    {
        Mrn = mrn;
        Email = email;
        FirstName = firstName;
        LastName = lastName;
        Birthday = birthday;
        PhoneNumbers = [PhoneNumber.Create(phoneNumber, "Home", true)];
        Emails = [EmailAddress.Create(email, true)];

        // initialize history with initial values
        FirstNameHistory = [new FirstNameHistoryEntry(firstName, timestamp, null)];
        LastNameHistory = [new LastNameHistoryEntry(lastName, timestamp, null)];
        AddressHistory = [];
    }

    public string Mrn { get; private set; }
    public string Email { get; private set; }
    public string FirstName { get; private set; }
    public string LastName { get; private set; }

    [JsonIgnore]
    public string? PreviousFirstName => FirstNameHistory
        .Where(x => x.ValidTo != null)
        .OrderByDescending(x => x.ValidTo)
        .FirstOrDefault()?.Value;

    [JsonIgnore]
    public string? PreviousLastName => LastNameHistory
        .Where(x => x.ValidTo != null)
        .OrderByDescending(x => x.ValidTo)
        .FirstOrDefault()?.Value;

    [JsonIgnore] public Address? Address => AddressHistory.LastOrDefault(x => x.ValidTo == null)?.Address;

    [JsonIgnore]
    public Address? PreviousAddress => AddressHistory
        .Where(x => x.ValidTo != null)
        .OrderByDescending(x => x.ValidTo)
        .FirstOrDefault()?.Address;

    public string? MiddleName { get; private set; }

    public string? Suffix { get; private set; }

    public string? PreferredName { get; private set; }

    public string? PreferredLanguage { get; private set; }

    public DateTime Birthday { get; private set; }

    public string? BirthSex { get; private set; }

    public string? GenderIdentity { get; private set; }

    public string? SexualOrientation { get; private set; }

    public string? Race { get; private set; }

    public string? Ethnicity { get; private set; }

    public string? TribalAffiliation { get; private set; }

    public decimal? HeightInCm { get; private set; }

    public decimal? WeightInKg { get; private set; }

    public List<PhoneNumber> PhoneNumbers { get; private set; }

    public List<EmailAddress> Emails { get; private set; } = [];

    public string? PreferredContactMethod { get; private set; }

    public string? PreferredContactName { get; private set; }

    public string? SocialSecurityNumber { get; private set; }

    public List<EmergencyContact> EmergencyContacts { get; private set; } = [];
    public List<FirstNameHistoryEntry> FirstNameHistory { get; private set; } = [];

    public List<LastNameHistoryEntry> LastNameHistory { get; private set; } = [];

    public List<AddressHistoryEntry> AddressHistory { get; private set; } = [];

    public List<PatientDocument> Documents { get; private set; } = [];

    public List<PatientMedication> Medications { get; private set; } = [];

    public List<PatientAllergy> Allergies { get; private set; } = [];

    public void SetPersonalInfo(SetPatientPersonalInfoCommand command)
    {
        // handle first and last name changes with history tracking
        SetFirstName(command.FirstName, command.Timestamp);
        SetLastName(command.LastName, command.Timestamp);

        MiddleName = command.MiddleName;
        Suffix = command.Suffix;
        PreferredName = command.PreferredName;
        PreferredLanguage = command.PreferredLanguage;
        Birthday = command.Birthday;
        BirthSex = command.BirthSex;
        GenderIdentity = command.GenderIdentity;
        SexualOrientation = command.SexualOrientation;
        Race = command.Race;
        Ethnicity = command.Ethnicity;
        TribalAffiliation = command.TribalAffiliation;
        HeightInCm = command.HeightInCm;
        WeightInKg = command.WeightInKg;
    }

    public void SetFirstName(string newFirstName, DateTime modifiedAt)
    {
        if (newFirstName == FirstName)
        {
            return;
        }

        // close current entry
        var currentIndex = FirstNameHistory.FindIndex(x => x.ValidTo == null);
        if (currentIndex != -1)
        {
            FirstNameHistory[currentIndex] = FirstNameHistory[currentIndex] with { ValidTo = modifiedAt };
        }

        // add new entry
        FirstNameHistory.Add(new FirstNameHistoryEntry(newFirstName, modifiedAt, null));

        // update the direct property
        FirstName = newFirstName;
    }

    public void SetLastName(string newLastName, DateTime modifiedAt)
    {
        if (newLastName == LastName)
        {
            return;
        }

        // close current entry
        var currentIndex = LastNameHistory.FindIndex(x => x.ValidTo == null);
        if (currentIndex != -1)
        {
            LastNameHistory[currentIndex] = LastNameHistory[currentIndex] with { ValidTo = modifiedAt };
        }

        // add new entry
        LastNameHistory.Add(new LastNameHistoryEntry(newLastName, modifiedAt, null));

        // update the direct property
        LastName = newLastName;
    }

    public void SetAddress(Address? newAddress, DateTime modifiedAt)
    {
        var currentAddress = Address;

        // check if addresses are the same by comparing all properties
        if (currentAddress != null && newAddress != null &&
            currentAddress.Street == newAddress.Street &&
            currentAddress.City == newAddress.City &&
            currentAddress.State == newAddress.State &&
            currentAddress.ZipCode == newAddress.ZipCode)
        {
            return;
        }

        // close current address entry if exists
        var currentIndex = AddressHistory.FindIndex(x => x.ValidTo == null);
        if (currentIndex != -1)
        {
            AddressHistory[currentIndex] = AddressHistory[currentIndex] with { ValidTo = modifiedAt };
        }

        // add new address to history if not null
        if (newAddress != null)
        {
            AddressHistory.Add(new AddressHistoryEntry(newAddress, modifiedAt, null));
        }
    }

    public void SetPreviousAddress(Address previousAddress, DateTime modifiedAt)
    {
        // check if this address already exists in history to avoid duplicates
        var addressExists = AddressHistory.Any(x =>
            x.Address.Street == previousAddress.Street &&
            x.Address.City == previousAddress.City &&
            x.Address.State == previousAddress.State &&
            x.Address.ZipCode == previousAddress.ZipCode);

        if (!addressExists)
        {
            // add previous address to history with a past date
            var pastDate = modifiedAt.AddDays(-1);
            AddressHistory.Add(new AddressHistoryEntry(previousAddress, pastDate, modifiedAt));
        }
    }

    public void SetPreviousFirstName(string previousFirstName, DateTime modifiedAt)
    {
        // check if this first name already exists in history to avoid duplicates
        var nameExists = FirstNameHistory.Any(x => x.Value == previousFirstName);

        if (!nameExists)
        {
            // add previous first name to history with a past date
            var pastDate = modifiedAt.AddDays(-1);
            FirstNameHistory.Add(new FirstNameHistoryEntry(previousFirstName, pastDate, modifiedAt));
        }
    }

    public void SetPreviousLastName(string previousLastName, DateTime modifiedAt)
    {
        // check if this last name already exists in history to avoid duplicates
        var nameExists = LastNameHistory.Any(x => x.Value == previousLastName);

        if (!nameExists)
        {
            // add previous last name to history with a past date
            var pastDate = modifiedAt.AddDays(-1);
            LastNameHistory.Add(new LastNameHistoryEntry(previousLastName, pastDate, modifiedAt));
        }
    }

    public void AddDocument(string docId, string documentType, List<string> filePaths, DateTime createdAt)
    {
        Documents.Add(new PatientDocument(docId, documentType, filePaths, createdAt));
    }

    public void DeleteDocuments(IEnumerable<string> documentsIds)
    {
        Documents.RemoveAll(x => documentsIds.Contains(x.Id));
    }

    public void SetMedications(IEnumerable<PatientMedicationRequest> medications)
    {
        Medications = medications.Select(x => new PatientMedication(x.Code, x.DisplayName)).ToList();
    }

    public void SetAllergies(IEnumerable<PatientAllergyRequest> allergies)
    {
        Allergies = allergies.Select(x => new PatientAllergy(x.Code, x.DisplayName, x.Reaction, x.Severity)).ToList();
    }

    public static Patient Create(string mrn, string email, string firstName, string lastName, DateTime birthday,
        string phoneNumber, DateTime timestamp) =>
        new(mrn, email, firstName, lastName, birthday, phoneNumber, timestamp);

    [JsonIgnore] public string FullName => $"{FirstName} {LastName}";

    public void SetContactInfo(SetPatientContactInfoCommand command)
    {
        PhoneNumbers = command.PhoneNumbers.Select(x => PhoneNumber.Create(x.Number, x.Type, x.IsPrimary)).ToList();
        Emails = command.Emails.Select(x => EmailAddress.Create(x.Email, x.IsPrimary)).ToList();
        PreferredContactMethod = command.PreferredContactMethod;
        PreferredContactName = command.PreferredContactName;
        SocialSecurityNumber = command.SocialSecurityNumber;
        EmergencyContacts = command.EmergencyContacts
            .Select(x => new EmergencyContact(x.Name, x.Relationship, x.PhoneNumber, x.Primary)).ToList();
    }
}

public record EmergencyContact(string Name, string Relationship, string PhoneNumber, bool Primary);

public record FirstNameHistoryEntry(string Value, DateTime ValidFrom, DateTime? ValidTo);

public record LastNameHistoryEntry(string Value, DateTime ValidFrom, DateTime? ValidTo);

public record AddressHistoryEntry(Address Address, DateTime ValidFrom, DateTime? ValidTo);

public record PatientDocument(string Id, string Type, List<string> FilePaths, DateTime CreatedAt);

public record PatientMedication(string Code, string DisplayName);

public record PatientAllergy(string Code, string DisplayName, string Reaction, string Severity);