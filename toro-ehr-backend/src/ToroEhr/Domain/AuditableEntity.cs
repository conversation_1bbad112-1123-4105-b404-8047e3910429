using Newtonsoft.Json;

namespace ToroEhr.Domain
{
    public class AuditableEntity : Entity
    {
        [JsonConstructor]

        protected AuditableEntity(DateTimeOffset createdAt, string createdBy)
        {
            CreatedAt = createdAt;
            CreatedBy = createdBy;
        }
        public DateTimeOffset CreatedAt { get; private set; }
        public DateTimeOffset? ModifiedAt { get; private set; }
        public string CreatedBy { get; private set; }
        public string? ModifiedBy { get; private set; }

        protected void SetModified(DateTimeOffset modifiedAt, string modifiedBy)
        {
            ModifiedAt = modifiedAt;
            ModifiedBy = modifiedBy;
        }
    }
}
