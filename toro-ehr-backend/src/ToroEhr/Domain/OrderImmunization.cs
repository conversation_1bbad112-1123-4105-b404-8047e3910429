using Newtonsoft.Json;
using ToroEhr.Enums;
using ToroEhr.Shared.Interfaces;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public class OrderImmunization : Order, ITimingStatusOrder
{
    [JsonConstructor]
    private OrderImmunization(string encounterId, string patientId, string immunizationId, string note, string name, string priority, string status,
        DateTimeOffset createdAt, string createdBy) :
        base(encounterId, patientId, name, priority, status, createdAt, createdBy)
    {
        ImmunizationId = immunizationId;
        Note = note;
    }
    public string ImmunizationId { get; private set; }
    public string? Note { get; private set; }
    public string? TimingStatus { get; private set; }
    public List<Administation> Administations { get; private set; } = [];


    public static OrderImmunization Create(
    string encounterId, string patientId, string immunizationId, string note, string name, string priority, DateTimeOffset createdAt, string createdBy)
    {
        return new OrderImmunization(encounterId, patientId, immunizationId, note, name, priority, OrderStatus.Draft,
            createdAt, createdBy);
    }

    public void Update(string immunizationId, string? note, string name)
    {
        ImmunizationId = immunizationId;
        Note = note;

        UpdateName(name);
    }

    public void UpdateTimingStatus(string timingStatus)
    {
        TimingStatus = timingStatus;
    }

    public void AddToAdministations(Administation administation)
    {
        Administations.Add(administation);
    }
}