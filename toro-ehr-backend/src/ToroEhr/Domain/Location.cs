using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class Location : DeactivatedEntity
{
    [JsonConstructor]
    private Location(string organizationId, string name, string classification, bool isDefault, string phoneNumber,
        string taxIdentificationNumber, int markMissedTime,
        decimal missedFee, int checkInStartOffsetHours, bool sameDayAppointment, Address? address, IposPaysConfig? iposPaysConfig)
    {
        OrganizationId = organizationId;
        Name = name;
        Classification = classification;
        IsDefault = isDefault;
        PhoneNumber = phoneNumber;
        TaxIdentificationNumber = taxIdentificationNumber;
        MarkMissedTime = markMissedTime;
        MissedFee = missedFee;
        CheckInStartOffsetHours = checkInStartOffsetHours;
        SameDayAppointment = sameDayAppointment;
        Address = address;
        IposPaysConfig = iposPaysConfig;
    }

    public string OrganizationId { get; private set; }
    public string Name { get; private set; }
    public string Classification { get; private set; }
    public bool IsDefault { get; private set; }
    public string PhoneNumber { get; private set; }
    public string TaxIdentificationNumber { get; private set; }
    public int MarkMissedTime { get; private set; }
    public decimal MissedFee { get; private set; }

    public int CheckInStartOffsetHours { get; private set; }
    public bool SameDayAppointment { get; private set; }
    public Address? Address { get; private set; }

    public IposPaysConfig? IposPaysConfig { get; private set; }

    [JsonIgnore] public string FormattedAddress => $"{Address?.Street}, {Address?.City}, {Address?.State} {Address?.ZipCode}";

    public void Update(string name, string classification, bool isDefault, string phoneNumber, string taxIdentificationNumber,
        int markMissedTime,
        decimal missedFee, int checkInStartOffsetHours, bool sameDayAppointment, Address address, IposPaysConfig? iposPaysConfig)
    {
        Name = name;
        Classification = classification;
        IsDefault = isDefault;
        PhoneNumber = phoneNumber;
        TaxIdentificationNumber = taxIdentificationNumber;
        MarkMissedTime = markMissedTime;
        MissedFee = missedFee;
        CheckInStartOffsetHours = checkInStartOffsetHours;
        SameDayAppointment = sameDayAppointment;
        Address = address;
        IposPaysConfig = iposPaysConfig;
    }

    public void UpdateDefault(bool isDefault)
    {
        IsDefault = isDefault;
    }

    public static Location Create(string organizationId, string name, string classification, bool isDefault,
        string phoneNumber,
        string taxIdentificationNumber, int markMissedTime,
        decimal missedFeeInCents, int checkInStartOffsetHours, bool sameDayAppointment, Address? address, IposPaysConfig? iposPaysConfig)
    {
        return new Location(organizationId, name, classification, isDefault, phoneNumber, taxIdentificationNumber,
            markMissedTime,
            missedFeeInCents, checkInStartOffsetHours, sameDayAppointment, address, iposPaysConfig);
    }
}

public record IposPaysConfig(string Tpn, string AuthKey, string CloudTpn, string CloudAuthKey);