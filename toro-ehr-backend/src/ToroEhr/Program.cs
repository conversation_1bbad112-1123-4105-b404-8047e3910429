using System.Security.Cryptography.X509Certificates;
using Amazon.Runtime;
using Amazon.S3;
using Coravel;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Http.Features;
using Raven.Client.Documents;
using Raven.Client.Documents.Conventions;
using Raven.Client.Documents.Indexes;
using Raven.Client.ServerWide.Operations.Configuration;
using Serilog;
using ToroEhr;
using ToroEhr.Enums;
using ToroEhr.Features.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Exceptions;
using ToroEhr.Infrastructure.Mediatr;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;

#if DEBUG
DotNetEnv.Env.Load(".env.local");
#endif

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddSingleton<IDocumentStore>(_ =>
{
    var documentStore = new DocumentStore
    {
        Urls = Config.Db.Urls,
        Database = Config.Db.Name,
        Conventions =
        {
            FindCollectionName = type =>
            {
                if (typeof(BaseEventEntity).IsAssignableFrom(type))
                    return "Events";

                return DocumentConventions.DefaultGetCollectionName(type);
            }
        }
    };

    if (Config.Db.Cert.IsNotNullOrWhiteSpace())
    {
        documentStore.Certificate = new X509Certificate2(Convert.FromBase64String(Config.Db.Cert));
    }

    documentStore.Initialize();

    documentStore.Maintenance.Send(new PutDatabaseSettingsOperation(Config.Db.Name, new Dictionary<string, string>
    {
        //{ "Indexing.SearchEngineType", "Lucene" }, //TODO: settings if we want all indexes to use lucene
        { "Indexing.Lucene.Analyzers.NGram.MaxGram", "10" }
    }));

    IndexCreation.CreateIndexes(typeof(Program).Assembly, documentStore);

    return documentStore;
});

// Load Serilog configuration and conditionally add Sentry sink
var loggerConfig = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .MinimumLevel.Information();

if (Config.Sentry.Dsn.IsNotNullOrWhiteSpace())
{
    // configure Sentry with ASP.NET Core integration
    builder.WebHost.UseSentry(options =>
    {
        options.Dsn = Config.Sentry.Dsn;
        options.Environment = Config.Sentry.Environment;
        options.TracesSampleRate = 0.1; // 10% of transactions for performance monitoring
        options.ProfilesSampleRate = 0.1; // 10% for profiling
        options.SendDefaultPii = false; // don't send personally identifiable information
        options.AttachStacktrace = true;
        options.Debug = false; // set to true only for debugging Sentry itself

        // apply exception filtering
        SentryExceptionFilter.ConfigureExceptionFilter(options);
    });

    // add Sentry sink to Serilog (this will use the same Sentry configuration)
    loggerConfig.WriteTo.Sentry(o =>
    {
        o.MinimumEventLevel = Serilog.Events.LogEventLevel.Error;
        // don't set DSN here - it will use the one from UseSentry above
    });
}

Log.Logger = loggerConfig.CreateLogger();

builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<Authenticator>();
builder.Services.AddSingleton<IAmazonS3>(_ => new AmazonS3Client(
    new BasicAWSCredentials(Config.S3.AccessKey, Config.S3.SecretKey), new
        AmazonS3Config
        {
            ServiceURL = "https://70b2c17e6e0605503bcd8546904b14d3.r2.cloudflarestorage.com",
            RequestChecksumCalculation = RequestChecksumCalculation.WHEN_REQUIRED,
            ResponseChecksumValidation = ResponseChecksumValidation.WHEN_REQUIRED
        }));
builder.Services.AddScoped<EmailService>();
builder.Services.AddScoped<S3FileService>();
builder.Services.AddScoped<SmsService>();
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(Program).Assembly));
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(AuthPipelineBehavior<,>));
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidatorPipelineBehavior<,>));
builder.Services.AddScheduler();
builder.Services.AddTransient<SlackService>();
builder.Services.AddTransient<EventHandlerService>();
builder.Services.AddTransient<MissedAppointmentSchedulerService>();

builder.Services.Scan(x =>
{
    x.FromAssemblies(typeof(Program).Assembly)
        .AddClasses(classes => classes.AssignableTo(typeof(IAuth<,>)))
        .AsImplementedInterfaces()
        .WithTransientLifetime();
});

builder.Services.Scan(x =>
{
    x.FromAssemblies(typeof(Program).Assembly)
        .AddClasses(classes => classes.AssignableTo(typeof(AbstractValidator<>)))
        .AsImplementedInterfaces()
        .WithScopedLifetime();
});

builder.Services.AddTransient<CodesImportHandlerService>();
builder.Services.AddHttpClient<CodesImportHandlerService>(client =>
{
    client.BaseAddress = new Uri(Config.Umls.UmlsBaseUrl);
    client.DefaultRequestHeaders.Add("User-Agent", "Toro");
});

builder.Services.AddTransient<CptCodesImporthandlerService>();
builder.Services.AddHttpClient<CptCodesImporthandlerService>(client =>
{
    client.BaseAddress = new Uri(Config.Ama.AmaBaseUrl);
    client.DefaultRequestHeaders.Add("User-Agent", "Toro");
});

builder.Services.AddHttpClient<IposService>(client =>
{
    client.BaseAddress = new Uri(Config.Ipos.IposUrl);
    client.DefaultRequestHeaders.Add("User-Agent", "Toro");
});

builder.Services.AddHttpClient<IposCloudService>(client =>
{
    client.BaseAddress = new Uri(Config.Ipos.IposCloudUrl);
    client.DefaultRequestHeaders.Add("User-Agent", "Toro");
});

builder.Services.AddHttpClient<OllamaService>(client =>
{
    client.BaseAddress = new Uri(Config.Ollama.BaseUrl);
    client.DefaultRequestHeaders.Add("User-Agent", "Toro");
    client.Timeout = TimeSpan.FromMinutes(2); // AI requests might take longer
});

builder.Services.AddHttpClient<WhisperService>(client =>
{
    client.DefaultRequestHeaders.Add("User-Agent", "Toro");
    client.Timeout = TimeSpan.FromMinutes(5); // Audio transcription might take longer
});


builder.Services.AddControllers()
    .AddNewtonsoftJson(options =>
    {
        // add smart enum converters for string-based enums
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<AppointmentStatus>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<OrderStatus>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<PaymentMethod>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<TransactionStatus>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<MessageType>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<UserRole>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<EmployeeRole>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<EncounterStatus>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<LaboratoryResultStatus>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<MeasurementType>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<OrderEntryType>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<OrderPriorityType>());
        options.SerializerSettings.Converters.Add(
            new SmartEnumStringJsonConverter<ReceiveNotificationPreferencesType>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<TransactionType>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<NotificationType>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<NotificationStatus>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<AppointmentTimeFilter>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<OrganizationType>());
        options.SerializerSettings.Converters.Add(new SmartEnumStringJsonConverter<QuestionnaireType>());
    });

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddOpenApiDocument(options =>
{
    // automatically register all smart enums from the current assembly
    options.SchemaSettings.AddSmartEnumSchemaProcessors(typeof(Program).Assembly);

    /*// Define the security scheme for AccessToken
    options.AddSecurity("accessToken", new OpenApiSecurityScheme
    {
        Type = OpenApiSecuritySchemeType.ApiKey,
        Name = "Authorization",
        In = OpenApiSecurityApiKeyLocation.Header,
        Description = "Enter your access token in the request header."
    });

    // Ensure API Key security requirement is applied to all operations
    options.OperationProcessors.Add(new NSwag.Generation.Processors.Security.OperationSecurityScopeProcessor("accessToken"));*/
});

builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
builder.Services.AddProblemDetails();
builder.Services.AddHealthChecks();

builder.Services.Configure<FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = 524288000; // 500 MB
});

var app = builder.Build();
var provider = app.Services;

app.MapHealthChecks("/healthz");

app.UseSerilogRequestLogging();

// enable Sentry tracing for performance monitoring (only if Sentry is configured)
if (Config.Sentry.Dsn.IsNotNullOrWhiteSpace())
{
    app.UseSentryTracing();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseOpenApi();
    app.UseSwaggerUi();
    app.UseReDoc();
}

app.UseExceptionHandler();

app.UseCors(options => options
    .AllowAnyOrigin()
    .AllowAnyHeader()
    .AllowAnyMethod());

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

provider.UseScheduler(scheduler =>
{
    scheduler.Schedule<CodesImportHandlerService>()
        .EveryFirstMondayInMonth();
    scheduler.Schedule<CptCodesImporthandlerService>()
        .EveryFirstMondayInMonth();

    scheduler.Schedule<EventHandlerService>()
        .EveryMinute()
        .PreventOverlapping(nameof(EventHandlerService));

    scheduler.Schedule<MissedAppointmentSchedulerService>()
        .Hourly()
        .PreventOverlapping(nameof(MissedAppointmentSchedulerService));
});
app.Run();


// Ensure the application shuts down cleanly and flushes the logs
Log.CloseAndFlush();