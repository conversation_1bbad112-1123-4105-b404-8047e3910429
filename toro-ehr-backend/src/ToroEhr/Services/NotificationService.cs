using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;

namespace ToroEhr.Services;

public static class NotificationService
{
    private const int NotificationRetentionDays = 90;

    public static async Task CreateAppointmentConfirmedNotification(IAsyncDocumentSession session, 
        string patientId, string appointmentId, DateTime timestamp)
    {
        var notification = Notification.Create(
            patientId,
            UserRole.Patient.Name,
            NotificationType.AppointmentConfirmed.Name,
            "Appointment Confirmed",
            "Your appointment has been confirmed.",
            appointmentId,
            "Appointment",
            timestamp);

        await session.StoreAsync(notification);
        
        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }

    public static async Task CreateAppointmentRequestedNotification(IAsyncDocumentSession session, 
        string employeeId, string locationId, string appointmentId, List<string> notificationPreferences, DateTime timestamp)
    {
        // check if employee wants to receive "Booked" notifications
        if (!notificationPreferences.Contains(ReceiveNotificationPreferencesType.Booked.Name))
            return;

        var notification = Notification.Create(
            employeeId,
            UserRole.Employee.Name,
            NotificationType.AppointmentRequested.Name,
            "New Appointment Requested",
            "A new appointment has been requested and may need your approval.",
            appointmentId,
            "Appointment",
            timestamp);

        await session.StoreAsync(notification);
        
        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }

    public static async Task CreateAppointmentRescheduledNotification(IAsyncDocumentSession session, 
        string employeeId, string appointmentId, List<string> notificationPreferences, DateTime timestamp)
    {
        // check if employee wants to receive "Rescheduled" notifications
        if (!notificationPreferences.Contains(ReceiveNotificationPreferencesType.Rescheduled.Name))
            return;

        var notification = Notification.Create(
            employeeId,
            UserRole.Employee.Name,
            NotificationType.AppointmentRescheduled.Name,
            "Appointment Rescheduled",
            "An appointment has been rescheduled.",
            appointmentId,
            "Appointment",
            timestamp);

        await session.StoreAsync(notification);
        
        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }

    public static async Task CreateAppointmentMissedNotification(IAsyncDocumentSession session,
        string employeeId, string appointmentId, List<string> notificationPreferences, DateTime timestamp)
    {
        // check if employee wants to receive "Missed" notifications
        if (!notificationPreferences.Contains(ReceiveNotificationPreferencesType.Missed.Name))
            return;

        var notification = Notification.Create(
            employeeId,
            UserRole.Employee.Name,
            NotificationType.AppointmentMissed.Name,
            "Appointment Missed",
            "A patient has missed their appointment.",
            appointmentId,
            "Appointment",
            timestamp);

        await session.StoreAsync(notification);

        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }

    public static async Task CreateAppointmentCanceledByPatientNotification(IAsyncDocumentSession session,
        string employeeId, string appointmentId, List<string> notificationPreferences, DateTime timestamp)
    {
        // check if employee wants to receive "Canceled" notifications
        if (!notificationPreferences.Contains(ReceiveNotificationPreferencesType.Canceled.Name))
            return;

        var notification = Notification.Create(
            employeeId,
            UserRole.Employee.Name,
            NotificationType.AppointmentCanceled.Name,
            "Appointment Canceled",
            "A patient has canceled their appointment.",
            appointmentId,
            "Appointment",
            timestamp);

        await session.StoreAsync(notification);

        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }

    public static async Task CreateAppointmentCanceledByEmployeeNotification(IAsyncDocumentSession session,
        string patientId, string appointmentId, DateTime timestamp)
    {
        // patients always receive cancellation notifications (no preference check)
        var notification = Notification.Create(
            patientId,
            UserRole.Patient.Name,
            NotificationType.AppointmentCanceled.Name,
            "Appointment Canceled",
            "Your appointment has been canceled.",
            appointmentId,
            "Appointment",
            timestamp);

        await session.StoreAsync(notification);

        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }

    public static async Task CreateCompleteProfileNotification(IAsyncDocumentSession session,
        string patientId, DateTime timestamp)
    {
        var notification = Notification.Create(
            patientId,
            UserRole.Patient.Name,
            NotificationType.CompleteProfile.Name,
            "Complete Your Profile",
            "Please complete your profile information to help us provide better care.",
            null,
            null,
            timestamp);

        await session.StoreAsync(notification);

        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }

    public static async Task CreateCompleteQuestionnairesNotification(IAsyncDocumentSession session,
        string patientId, DateTime timestamp)
    {
        var notification = Notification.Create(
            patientId,
            UserRole.Patient.Name,
            NotificationType.CompleteQuestionnaires.Name,
            "Complete Questionnaires",
            "Please complete the required questionnaires before your appointment.",
            null,
            null,
            timestamp);

        await session.StoreAsync(notification);

        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }

    public static async Task CreateQuestionnaireUpdatedNotification(IAsyncDocumentSession session,
        string patientId, string questionnaireTitle, string questionnaireId, DateTime timestamp)
    {
        var notification = Notification.Create(
            patientId,
            UserRole.Patient.Name,
            NotificationType.QuestionnaireUpdated.Name,
            "Questionnaire Updated",
            $"The questionnaire '{questionnaireTitle}' has been updated with new required questions. Please review and complete it.",
            questionnaireId,
            "Questionnaire",
            timestamp);

        await session.StoreAsync(notification);

        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }

    public static async Task CreateMessageReceivedNotification(IAsyncDocumentSession session,
        string userId, string userRole, string senderName, string messageType, string encounterId,
        List<string>? notificationPreferences, DateTime timestamp)
    {
        var notification = Notification.Create(
            userId,
            userRole,
            NotificationType.MessageReceived.Name,
            "New Message Received",
            $"You have received a new {messageType.ToLower()} message from {senderName}.",
            encounterId,
            "Encounter",
            timestamp);

        await session.StoreAsync(notification);

        // set document expiration
        var metadata = session.Advanced.GetMetadataFor(notification);
        metadata["@expires"] = timestamp.AddDays(NotificationRetentionDays);
    }
}
