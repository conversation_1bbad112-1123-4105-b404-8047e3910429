using System.Text;
using Newtonsoft.Json;
using ToroEhr.Infrastructure;

namespace ToroEhr.Services;

public sealed class WhisperService
{
    private readonly HttpClient _httpClient;

    public WhisperService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<string> TranscribeAudioAsync(IFormFile audioFile, string model = "small")
    {
        // Determine port based on model
        var port = model.ToLowerInvariant() switch
        {
            "turbo" => "5000",
            "distil" => "5002",
            _ => "5001" // default to small
        };
        var baseUrl = Config.Whisper.BaseUrl;
        var fullUrl = $"{baseUrl}:{port}/transcribe";

        using var content = new MultipartFormDataContent();

        // Add the audio file
        var fileContent = new StreamContent(audioFile.OpenReadStream());
        fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(audioFile.ContentType);
        content.Add(fileContent, "audio_file", audioFile.FileName);

        var response = await _httpClient.PostAsync(fullUrl, content);

        if (response.IsSuccessStatusCode == false)
        {
            SentrySdk.CaptureMessage($"{response.ReasonPhrase}||{response.Content.ReadAsStringAsync().Result}");
            return "";
        }
        
        var responseContent = await response.Content.ReadAsStringAsync();

        // Try to parse as JSON first (most self-hosted APIs return JSON)
        try
        {
            var transcriptionResponse = JsonConvert.DeserializeObject<WhisperResponse>(responseContent);
            return transcriptionResponse?.Text ?? responseContent.Trim();
        }
        catch
        {
            // If JSON parsing fails, return the raw response (some APIs return plain text)
            return responseContent.Trim();
        }
    }
}

public class WhisperResponse
{
    [JsonProperty("text")]
    public string Text { get; set; } = null!;
}
