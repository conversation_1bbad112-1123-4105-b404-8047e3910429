using System.Text;
using Newtonsoft.Json;

namespace ToroEhr.Services;

public sealed class OllamaService
{
    private readonly HttpClient _httpClient;

    public OllamaService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<string> FormatTextAsync(string inputText)
    {
        var prompt = $"Format the following raw transcript by adding appropriate punctuation, capitalization, and sentence breaks. Do not rephrase, summarize, interpret, or add any words. Return only the formatted version of the input:\n\n{inputText}";

        var request = new OllamaRequest
        {
            Model = "phi3:mini",
            Prompt = prompt,
            Stream = false
        };

        var jsonContent = JsonConvert.SerializeObject(request);
        var httpContent = new StringContent(jsonContent, Encoding.UTF8, "application/json");

        var response = await _httpClient.PostAsync("api/generate", httpContent);
        response.EnsureSuccessStatusCode();

        var responseContent = await response.Content.ReadAsStringAsync();
        var ollamaResponse = JsonConvert.DeserializeObject<OllamaResponse>(responseContent);

        return ollamaResponse?.Response ?? inputText;
    }
}

public class OllamaRequest
{
    [JsonProperty("model")]
    public string Model { get; set; } = null!;

    [JsonProperty("prompt")]
    public string Prompt { get; set; } = null!;

    [JsonProperty("stream")]
    public bool Stream { get; set; }
}

public class OllamaResponse
{
    [JsonProperty("model")]
    public string Model { get; set; } = null!;

    [JsonProperty("created_at")]
    public DateTime CreatedAt { get; set; }

    [JsonProperty("response")]
    public string Response { get; set; } = null!;

    [JsonProperty("done")]
    public bool Done { get; set; }
}
