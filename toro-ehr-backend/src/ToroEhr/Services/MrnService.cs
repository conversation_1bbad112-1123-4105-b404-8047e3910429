using Raven.Client.Documents.Session;
using System.Security.Cryptography;
using Raven.Client.Documents;

namespace ToroEhr.Services;

public static class MrnService
{

    /// <summary>
    /// Generates a unique MRN using timestamp + random format (YYMMDD + 4 random digits)
    /// Example: 2406301234 (June 30, 2024 + random 1234)
    /// Note: For RavenDB, uniqueness is checked within the same session/transaction
    /// </summary>
    public static async Task<string> GenerateUniqueMrnAsync(IAsyncDocumentSession session, CancellationToken cancellationToken = default)
    {
        const int maxAttempts = 100;
        var attempts = 0;

        while (attempts < maxAttempts)
        {
            var mrn = GenerateMrn();

            if (await IsMrnUniqueInSessionAsync(session, mrn, cancellationToken))
            {
                return mrn;
            }

            attempts++;
        }

        throw new InvalidOperationException($"Failed to generate unique MRN after {maxAttempts} attempts");
    }

    /// <summary>
    /// Checks MRN uniqueness within the current session (includes pending changes)
    /// </summary>
    private static async Task<bool> IsMrnUniqueInSessionAsync(IAsyncDocumentSession session, string mrn, CancellationToken cancellationToken)
    {
        var existingPatient = await session
            .Query<Domain.Patient>()
            .Where(p => p.Mrn == mrn)
            .FirstOrDefaultAsync(cancellationToken);

        return existingPatient == null;
    }

    /// <summary>
    /// Validates MRN format (10 characters, starts with 2 digits for year)
    /// </summary>
    public static bool IsValidMrnFormat(string mrn)
    {
        if (string.IsNullOrWhiteSpace(mrn) || mrn.Length != 10)
            return false;

        // Check if all characters are digits
        return mrn.All(char.IsDigit);
    }

    private static string GenerateMrn()
    {
        // Generate timestamp part (YYMMDD)
        var now = DateTime.UtcNow;
        var timestampPart = now.ToString("yyMMdd");

        // Generate 4 random digits
        var randomPart = GenerateRandomDigits(4);

        return timestampPart + randomPart;
    }

    private static string GenerateRandomDigits(int length)
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[4]; // 4 bytes for random number generation
        rng.GetBytes(bytes);

        var randomNumber = BitConverter.ToUInt32(bytes, 0);
        var randomString = (randomNumber % (uint)Math.Pow(10, length)).ToString($"D{length}");

        return randomString;
    }


}
