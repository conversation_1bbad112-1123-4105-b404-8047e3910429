using Coravel.Invocable;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;

namespace ToroEhr.Services;

public class MissedAppointmentSchedulerService : IInvocable, ICancellableInvocable
{
    private readonly IDocumentStore _store;
    private readonly ILogger<MissedAppointmentSchedulerService> _logger;

    public CancellationToken CancellationToken { get; set; }

    public MissedAppointmentSchedulerService(IDocumentStore store, ILogger<MissedAppointmentSchedulerService> logger)
    {
        _store = store;
        _logger = logger;
    }

    public async Task Invoke()
    {
        if (CancellationToken.IsCancellationRequested) return;
        await ProcessMissedAppointments(CancellationToken);
    }

    private async Task ProcessMissedAppointments(CancellationToken cancellationToken)
    {
        try
        {
            using IAsyncDocumentSession session = _store.OpenAsyncSession();
            
            var currentTime = DateTimeOffset.UtcNow;
            
            // get appointments that could potentially be missed
            var appointments = await session.Query<Appointment>()
                .Include(x => x.LocationId)
                .Include(x => x.EncounterId)
                .Include(x => x.EmployeeId)
                .Where(x => x.Status == AppointmentStatus.Confirmed.Name ||
                           x.Status == AppointmentStatus.CheckedIn.Name)
                .Where(x => x.StartAt < currentTime)
                .ToListAsync(cancellationToken);

            foreach (var appointment in appointments)
            {
                if (cancellationToken.IsCancellationRequested) break;

                var location = await session.LoadAsync<Location>(appointment.LocationId, cancellationToken);
                var encounter = await session.LoadAsync<Encounter>(appointment.EncounterId, cancellationToken);
                if (location == null || encounter == null) continue;

                // calculate if appointment should be marked as missed
                var missedThreshold = appointment.StartAt.AddHours(location.MarkMissedTime);

                if (currentTime >= missedThreshold)
                {
                    appointment.MarkAsMissed();
                    encounter.MarkAsMissed();

                    // get location employee for notification preferences
                    var locationEmployee = await session.Query<LocationEmployee>()
                        .Where(x => x.EmployeeId == appointment.EmployeeId && x.LocationId == appointment.LocationId)
                        .FirstOrDefaultAsync(cancellationToken);

                    // create notification for practitioner about missed appointment
                    if (locationEmployee != null)
                    {
                        await NotificationService.CreateAppointmentMissedNotification(session, appointment.EmployeeId, appointment.Id, locationEmployee.ReceivedNotificationPreferences, currentTime.DateTime);
                    }
                }
            }
            
            await session.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing missed appointments");
            throw;
        }
    }
}
