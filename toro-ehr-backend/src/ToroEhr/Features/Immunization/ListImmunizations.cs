using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using Raven.Client.Documents.Queries;

namespace ToroEhr.Features.Immunization;

public sealed record ListImmunizationsQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<ImmunizationResponse>>;

public record ImmunizationResponse(
    string Id,
    string Code,
    string CodeSystem,
    string CodeSystemName,
    string CodeSystemVersion,
    string DisplayName,
    string ShortDescription,
    string Note,
    bool NonVaccine,
    string Status
    );

internal sealed class ListImmunizationsAuth : IAuth<ListImmunizationsQuery, PaginatedList<ImmunizationResponse>>
{
    public ListImmunizationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ListImmunizationsHandler : IRequestHandler<ListImmunizationsQuery, PaginatedList<ImmunizationResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;
    public ListImmunizationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<ImmunizationResponse>> Handle(ListImmunizationsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        IRavenQueryable<Domain.Immunization> dbQuery = session.Query<Domain.Immunization>();

        if (_user.IsPatient)
        {
            dbQuery = dbQuery
                .Where(x => x.ShowToPatient);
        }

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery
                .Search(x => x.Code, $"{query.PagedSearchParams.SearchParam}*")
                .Search(x => x.DisplayName, $"{query.PagedSearchParams.SearchParam}*");
        }

        var allergies = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<ImmunizationResponse>.Create(
            allergies.Select(x => new ImmunizationResponse(x.Id, x.Code, x.CodeSystem, x.CodeSystemName, 
            x.CodeSystemVersion, x.DisplayName, x.ShortDescription, x.Note, x.NonVaccine, x.Status)).ToList(), stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}