using ClosedXML.Excel;
using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared.Models;

namespace ToroEhr.Features.Immunization;

public sealed record ImportImmunizationsCommand(IFormFile File) : AuthRequest<Unit>;

public sealed class ImportImmunizationRequet : CodingRequest
{
    public string ShortDesciption { get; set; } = string.Empty;
    public string Note { get; set; } = string.Empty;
    public string NonVaccine { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

internal sealed class ImportImmunizationsCommandValidator : AbstractValidator<ImportImmunizationsCommand>
{
    public ImportImmunizationsCommandValidator()
    {
        RuleFor(x => x.File).NotEmpty();
    }
}

internal sealed class ImportImmunizationsAuth : IAuth<ImportImmunizationsCommand, Unit>
{
    public ImportImmunizationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class ImportImmunizationsHandler : IRequestHandler<ImportImmunizationsCommand, Unit>
{
    private readonly IDocumentStore _store;

    public ImportImmunizationsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ImportImmunizationsCommand request, CancellationToken cancellationToken)
    {
        var immunizations = await ParseImmunizationsFile(request.File);

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        foreach (var immunizationRow in immunizations)
        {
            var immunization = Domain.Immunization.Create(immunizationRow.Code, "2.16.840.1.113883.12.292", "CVX",
                immunizationRow.CodeSystemVersion, immunizationRow.DisplayName, immunizationRow.ShortDesciption,
                immunizationRow.Note, immunizationRow.NonVaccine == "True", immunizationRow.Status);
            await session.StoreAsync(immunization, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }

    private async Task<List<ImportImmunizationRequet>> ParseImmunizationsFile(IFormFile file)
    {
        var records = new List<ImportImmunizationRequet>();
        var sheetName = "Web_cvx";

        using (var stream = new MemoryStream())
        {
            await file.CopyToAsync(stream);
            stream.Position = 0; // Reset stream position

            using (var workbook = new XLWorkbook(stream))
            {
                var worksheet = workbook.Worksheet(sheetName);
                if (worksheet == null)
                {
                    throw new Exception($"Sheet '{sheetName}' not found in the Excel file.");
                }

                int startRow = 2; // Start reading from row 2

                foreach (var row in worksheet.RowsUsed().Skip(startRow - 1))
                {
                    var data = new ImportImmunizationRequet
                    {
                        Code = row.Cell(1).GetValue<string>().Trim(),  // Column A
                        ShortDesciption = row.Cell(2).GetValue<string>(), // Column B
                        DisplayName = row.Cell(3).GetValue<string>(),     // Column C
                        Note = row.Cell(4).GetValue<string>(),     // Column D
                        Status = row.Cell(5).GetValue<string>(),     // Column E
                        NonVaccine = row.Cell(7).GetValue<string>(), // Column G
                        CodeSystemVersion = row.Cell(8).GetValue<string>(), // Column H
                    };

                    records.Add(data);
                }
            }
        }

        return records;
    }
}