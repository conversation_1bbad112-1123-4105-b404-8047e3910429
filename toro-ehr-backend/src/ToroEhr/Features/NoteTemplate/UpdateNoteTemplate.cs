using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.NoteTemplate;

public sealed record UpdateNoteTemplateCommand(
    string Id,
    string Name,
    string Classification,
    string? Specialization,
    string SpecialityCode,
    string DocumentType,
    List<NoteTemplateFieldRequest> Fields,
    List<string> LocationIds) : AuthRequest<string>;

internal sealed class UpdateNoteTemplateAuth : IAuth<UpdateNoteTemplateCommand, string>
{
    public UpdateNoteTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public sealed class UpdateNoteTemplateCommandValidator : AbstractValidator<UpdateNoteTemplateCommand>
{
    public UpdateNoteTemplateCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Classification).NotEmpty();
        RuleFor(x => x.SpecialityCode).NotEmpty();
        RuleFor(x => x.DocumentType).NotEmpty();
        RuleFor(x => x.Fields).NotEmpty().WithMessage("At least one field is required.");
    }
}

internal sealed class UpdateNoteTemplateHandler : IRequestHandler<UpdateNoteTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public UpdateNoteTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(UpdateNoteTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.NoteTemplate noteTemplate = await session.LoadAsync<Domain.NoteTemplate>(command.Id, cancellationToken);
        
        Guard.AgainstNotFound(noteTemplate,
            new AppError("NoteTemplate.NotFound", $"Note Template with 'id:' {command.Id} not found!"));

        List<string> locationIds = [];
        
        if (_user.SelectedUserRole == UserRole.Employee.Name &&
            _user.SelectedLocationEmployeeRoles!.Contains(EmployeeRole.Practitioner.Name))
        {
            locationIds.Add(_user.SelectedLocationId!);
        }
        else
        {
            locationIds.AddRange(command.LocationIds);
        }

        noteTemplate.Update(
            command.Name,
            command.Classification,
            command.Specialization,
            command.DocumentType,
            command.SpecialityCode,
            command.Fields.Select(x => new NoteTemplateField(x.Name, x.Value, x.IsRequired)).ToList(),
            locationIds);

        await session.StoreAsync(noteTemplate, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return noteTemplate.Id;
    }
}
