using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.NoteTemplate;

public sealed record ListNoteTemplatesQuery(PagedSearchParams PagedSearchParams, string? SpecialtyFilter)
    : AuthRequest<PaginatedList<NoteTemplateResponse>>;

public sealed record NoteTemplateResponse(
    string Id,
    string Name,
    string Classification,
    string? Specialization,
    string DocumentType,
    string Organization,
    string Locations,
    string Creator);

internal sealed class ListNoteTemplatesAuth : IAuth<ListNoteTemplatesQuery, PaginatedList<NoteTemplateResponse>>
{
    public ListNoteTemplatesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class
    ListNoteTemplatesHandler : IRequestHandler<ListNoteTemplatesQuery, PaginatedList<NoteTemplateResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListNoteTemplatesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<NoteTemplateResponse>> Handle(ListNoteTemplatesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Domain.NoteTemplate> dbQuery = session
            .Query<Domain.NoteTemplate>()
            .Where(x => x.LocationIds.Any(l => l == _user.SelectedLocationId));

        if (query.SpecialtyFilter.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery.Where(x => x.SpecialityCode == query.SpecialtyFilter);
        }

        List<Domain.NoteTemplate> templates = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        // collect all unique entity IDs for batch loading
        var organizationIds = templates.Select(t => t.OrganizationId).Distinct().ToList();
        var locationIds = templates.SelectMany(t => t.LocationIds).Distinct().ToList();
        var creatorIds = templates.Select(t => t.CreatorId).Distinct().ToList();

        // batch load all related entities
        var organizations = await session.LoadAsync<Domain.Organization>(organizationIds, cancellationToken);
        var locations = await session.LoadAsync<Domain.Location>(locationIds, cancellationToken);
        var creators = await session.LoadAsync<Domain.Employee>(creatorIds, cancellationToken);

        return PaginatedList<NoteTemplateResponse>.Create(
            templates.Select(t =>
            {
                var organization = organizations[t.OrganizationId];
                var templateLocations = t.LocationIds.Select(id => locations[id]).Where(l => l != null);
                var creator = creators[t.CreatorId];

                return new NoteTemplateResponse(t.Id, t.Name, t.Classification, t.Specialization, t.DocumentType,
                    organization?.Name ?? "Unknown Organization",
                    string.Join(", ", templateLocations.Select(l => l.Name)),
                    creator?.FullName ?? "Unknown Creator");
            }).ToList(),
            stats.TotalResults, query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}