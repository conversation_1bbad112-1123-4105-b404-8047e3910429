using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Notification;

public sealed record DeleteNotificationCommand(string NotificationId) : AuthRequest<Unit>;

internal sealed class DeleteNotificationAuth : IAuth<DeleteNotificationCommand, Unit>
{
    public DeleteNotificationAuth(Authenticator authenticator)
    {
        // any authenticated user can delete their own notifications
        var user = authenticator.User;
    }
}

internal sealed class DeleteNotificationValidator : AbstractValidator<DeleteNotificationCommand>
{
    public DeleteNotificationValidator()
    {
        RuleFor(x => x.NotificationId).NotEmpty();
    }
}

internal sealed class DeleteNotificationHandler : IRequestHandler<DeleteNotificationCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public DeleteNotificationHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(DeleteNotificationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Notification notification = await session.LoadAsync<Domain.Notification>(command.NotificationId, cancellationToken);
        Guard.AgainstNotFound(notification, new("Notification.NotFound", $"Notification with id '{command.NotificationId}' not found"));

        // ensure user can only delete their own notifications
        string userId = _user.IsPatient ? _user.PatientId! : _user.EmployeeId!;
        if (notification.UserId != userId)
        {
            throw new UnauthorizedAccessException("You can only delete your own notifications");
        }

        session.Delete(notification);

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}
