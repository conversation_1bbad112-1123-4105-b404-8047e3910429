using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Notification;

public sealed record GetUnreadNotificationCountQuery : AuthRequest<int>;

internal sealed class GetUnreadNotificationCountAuth : IAuth<GetUnreadNotificationCountQuery, int>
{
    public GetUnreadNotificationCountAuth(Authenticator authenticator)
    {
        // any authenticated user can get their unread notification count
        var user = authenticator.User;
    }
}

internal sealed class GetUnreadNotificationCountHandler : IRequestHandler<GetUnreadNotificationCountQuery, int>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetUnreadNotificationCountHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<int> Handle(GetUnreadNotificationCountQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // determine user ID based on user type
        string userId = _user.IsPatient ? _user.PatientId! : _user.EmployeeId!;

        int count = await session.Query<Domain.Notification>()
            .Where(x => x.UserId == userId && x.Status == NotificationStatus.Unread.Name)
            .CountAsync(cancellationToken);

        return count;
    }
}
