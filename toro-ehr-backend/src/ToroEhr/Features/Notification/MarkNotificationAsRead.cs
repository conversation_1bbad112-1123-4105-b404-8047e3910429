using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Notification;

public sealed record MarkNotificationAsReadCommand(List<string> NotificationIds) : AuthRequest<Unit>;

internal sealed class MarkNotificationAsReadAuth : IAuth<MarkNotificationAsReadCommand, Unit>
{
    public MarkNotificationAsReadAuth(Authenticator authenticator)
    {
        // any authenticated user can mark their own notifications as read
        var user = authenticator.User;
    }
}

internal sealed class MarkNotificationAsReadValidator : AbstractValidator<MarkNotificationAsReadCommand>
{
    public MarkNotificationAsReadValidator()
    {
        RuleFor(x => x.NotificationIds)
            .NotEmpty()
            .WithMessage("At least one notification ID must be provided");

        RuleForEach(x => x.NotificationIds)
            .NotEmpty()
            .WithMessage("Notification ID cannot be empty");
    }
}

internal sealed class MarkNotificationAsReadHandler : IRequestHandler<MarkNotificationAsReadCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public MarkNotificationAsReadHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(MarkNotificationAsReadCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // load all notifications in a single batch
        var notifications = await session.LoadAsync<Domain.Notification>(command.NotificationIds, cancellationToken);

        string userId = _user.IsPatient ? _user.PatientId! : _user.EmployeeId!;

        foreach (var notificationId in command.NotificationIds)
        {
            var notification = notifications[notificationId];

            // skip if notification not found (idempotent approach)
            if (notification == null)
                continue;

            // ensure user can only mark their own notifications as read
            if (notification.UserId != userId)
            {
                throw new UnauthorizedAccessException("You can only mark your own notifications as read");
            }

            notification.MarkAsRead(command.Timestamp);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}
