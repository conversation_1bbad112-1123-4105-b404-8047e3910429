using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Notification;

public sealed record GetUserNotificationsQuery(PagedSearchParams PagedSearchParams) : AuthRequest<PaginatedList<NotificationResponse>>;

public sealed record NotificationResponse(
    string Id,
    NotificationType NotificationType,
    NotificationStatus Status,
    string Title,
    string Message,
    string? RelatedEntityId,
    string? RelatedEntityType,
    DateTime CreatedAt,
    DateTime? ReadAt,
    bool ActionCompleted);

internal sealed class GetUserNotificationsAuth : IAuth<GetUserNotificationsQuery, PaginatedList<NotificationResponse>>
{
    public GetUserNotificationsAuth(Authenticator authenticator)
    {
        // any authenticated user can get their own notifications
        var user = authenticator.User;
    }
}

internal sealed class GetUserNotificationsHandler : IRequestHandler<GetUserNotificationsQuery, PaginatedList<NotificationResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetUserNotificationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<NotificationResponse>> Handle(GetUserNotificationsQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // determine user ID based on user type
        string userId = _user.IsPatient ? _user.PatientId! : _user.EmployeeId!;

        IRavenQueryable<Domain.Notification> dbQuery = session.Query<Domain.Notification>()
            .Where(x => x.UserId == userId)
            .OrderByDescending(x => x.CreatedAt);

        var notifications = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(cancellationToken);

        var response = new List<NotificationResponse>();

        foreach (var notification in notifications)
        {
            bool actionCompleted = await DetermineActionCompletionStatus(session, notification, cancellationToken);

            response.Add(new NotificationResponse(
                notification.Id,
                NotificationType.FromName(notification.NotificationType),
                NotificationStatus.FromName(notification.Status),
                notification.Title,
                notification.Message,
                notification.RelatedEntityId,
                notification.RelatedEntityType,
                notification.CreatedAt,
                notification.ReadAt,
                actionCompleted));
        }

        return PaginatedList<NotificationResponse>.Create(
            response,
            stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize);
    }

    private static async Task<bool> DetermineActionCompletionStatus(IAsyncDocumentSession session,
        Domain.Notification notification, CancellationToken cancellationToken)
    {
        return notification.NotificationType switch
        {
            var type when type == NotificationType.AppointmentRequested.Name =>
                await IsAppointmentRequestActionCompleted(session, notification.RelatedEntityId, cancellationToken),

            var type when type == NotificationType.AppointmentRescheduled.Name =>
                await IsAppointmentRescheduleActionCompleted(session, notification.RelatedEntityId, cancellationToken),

            var type when type == NotificationType.AppointmentMissed.Name =>
                await IsAppointmentMissedActionCompleted(session, notification.RelatedEntityId, cancellationToken),

            var type when type == NotificationType.AppointmentCanceled.Name =>
                await IsAppointmentCanceledActionCompleted(session, notification.RelatedEntityId, cancellationToken),

            var type when type == NotificationType.CompleteProfile.Name =>
                await IsCompleteProfileActionCompleted(session, notification.UserId, cancellationToken),

            var type when type == NotificationType.CompleteQuestionnaires.Name =>
                await IsCompleteQuestionnairesActionCompleted(session, notification.UserId, cancellationToken),

            var type when type == NotificationType.QuestionnaireUpdated.Name =>
                await IsQuestionnaireUpdatedActionCompleted(session, notification.UserId, cancellationToken),

            // AppointmentConfirmed and MessageReceived notifications don't typically have actions
            _ => false
        };
    }

    private static async Task<bool> IsAppointmentRequestActionCompleted(IAsyncDocumentSession session,
        string appointmentId, CancellationToken cancellationToken)
    {
        var appointment = await session.LoadAsync<Domain.Appointment>(appointmentId, cancellationToken);
        if (appointment == null) return true; // appointment deleted = action completed

        // action is completed if appointment is no longer pending
        return appointment.Status != AppointmentStatus.Pending.Name;
    }

    private static async Task<bool> IsAppointmentRescheduleActionCompleted(IAsyncDocumentSession session,
        string appointmentId, CancellationToken cancellationToken)
    {
        var appointment = await session.LoadAsync<Domain.Appointment>(appointmentId, cancellationToken);
        if (appointment == null) return true; // appointment deleted = action completed

        // for rescheduled appointments, action is completed when practitioner acknowledges or takes action
        // this could be checking if appointment status changed or if practitioner viewed it
        // for now, we'll consider it completed if status is confirmed or if it's been read
        return appointment.Status == AppointmentStatus.Confirmed.Name;
    }

    private static async Task<bool> IsAppointmentMissedActionCompleted(IAsyncDocumentSession session,
        string appointmentId, CancellationToken cancellationToken)
    {
        var appointment = await session.LoadAsync<Domain.Appointment>(appointmentId, cancellationToken);
        if (appointment == null) return true; // appointment deleted = action completed

        // for missed appointments, action might be completed when:
        // 1. appointment is rescheduled (status changed from missed)
        // 2. payment is processed for missed appointment fee
        // 3. practitioner marks it as handled
        return appointment.Status != AppointmentStatus.Missed.Name;
    }

    private static async Task<bool> IsAppointmentCanceledActionCompleted(IAsyncDocumentSession session,
        string appointmentId, CancellationToken cancellationToken)
    {
        var appointment = await session.LoadAsync<Domain.Appointment>(appointmentId, cancellationToken);
        if (appointment == null) return true; // appointment deleted = action completed

        // for canceled appointments, the notification is informational
        // action is always completed since cancellation is final
        return true;
    }

    private static async Task<bool> IsCompleteProfileActionCompleted(IAsyncDocumentSession session,
        string patientId, CancellationToken cancellationToken)
    {
        var patient = await session.LoadAsync<Domain.Patient>(patientId, cancellationToken);
        if (patient == null) return true; // patient deleted = action completed

        // check if patient has completed basic profile information
        // consider profile complete if patient has: first name, last name, birthday, email, phone, and birth sex
        return !string.IsNullOrWhiteSpace(patient.FirstName) &&
               !string.IsNullOrWhiteSpace(patient.LastName) &&
               patient.Birthday != default &&
               !string.IsNullOrWhiteSpace(patient.Email) &&
               patient.PhoneNumbers.Any() &&
               !string.IsNullOrWhiteSpace(patient.BirthSex);
    }

    private static async Task<bool> IsCompleteQuestionnairesActionCompleted(IAsyncDocumentSession session,
        string patientId, CancellationToken cancellationToken)
    {
        var patientOrganizationsIds = await session.Query<OrganizationPatient>()
            .Where(x => x.PatientId == patientId)
            .Select(x => x.OrganizationId)
            .ToListAsync(token: cancellationToken);

        if (!patientOrganizationsIds.Any()) return true;

        // get all latest questionnaires for patient's organizations (both general and location-specific)
        var questionnaires = await session.Query<Domain.Questionnaire>()
            .Where(x => x.IsLatest && (x.LocationId == null || // general questionnaires
                       x.LocationId.In(patientOrganizationsIds))) // location-specific
            .ToListAsync(token: cancellationToken);

        if (!questionnaires.Any()) return true;

        // get patient's questionnaire responses
        var responses = await session.Query<QuestionnaireResponse>()
            .Where(x => x.PatientId == patientId)
            .ToListAsync(token: cancellationToken);

        // check if patient has responded to all required questionnaires
        var responseQuestionnaireIds = responses.Select(r => r.QuestionnaireId).ToHashSet();
        return questionnaires.All(q => responseQuestionnaireIds.Contains(q.QuestionnaireId));
    }

    private static async Task<bool> IsQuestionnaireUpdatedActionCompleted(IAsyncDocumentSession session,
        string patientId, CancellationToken cancellationToken)
    {
        // for questionnaire updated notifications, action is completed when patient has reviewed/completed
        // the updated questionnaires. This is similar to complete questionnaires logic.
        return await IsCompleteQuestionnairesActionCompleted(session, patientId, cancellationToken);
    }
}
