using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;
using ToroEhr.Indexes;
using DocumentFormat.OpenXml.Spreadsheet;
using Raven.Client.Documents.Queries;

namespace ToroEhr.Features.Medication;

public sealed record ListMedicationsQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<CodingResponse>>;

internal sealed class ListMedicationAuth : IAuth<ListMedicationsQuery, PaginatedList<CodingResponse>>
{
    public ListMedicationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ListLocationsHandler : IRequestHandler<ListMedicationsQuery, PaginatedList<CodingResponse>>
{
    private readonly IDocumentStore _store;

    public ListLocationsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<PaginatedList<CodingResponse>> Handle(ListMedicationsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Medications_Search.Entry> dbQuery = session
            .Query<Medications_Search.Entry, Medications_Search>()
            .ProjectInto<Medications_Search.Entry>()
            .Include(x => x.MedicationId)
            .Where(x => x.SourceAbbreviation == "RXNORM");

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery.Search(x => x.Search, $"{query.PagedSearchParams.SearchParam}*",
                options: SearchOptions.And, @operator: SearchOperator.And);
        }

        var entries = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        List<Domain.Medication> medications = new List<Domain.Medication> ();
        foreach (var entry in entries) {
            medications.Add(await session.LoadAsync<Domain.Medication>(entry.MedicationId, cancellationToken));
        }

        return PaginatedList<CodingResponse>.Create(
            medications.Select(x => new CodingResponse(x.Id, x.SourceCode, string.Empty, x.SourceAbbreviation,
                string.Empty, x.TermString)).ToList(), stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}