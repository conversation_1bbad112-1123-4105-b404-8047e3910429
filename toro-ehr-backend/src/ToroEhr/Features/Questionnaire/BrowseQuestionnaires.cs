using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Questionnaire;

public sealed record BrowseQuestionnairesQuery(PagedSearchParams PagedSearchParams, string LocationFilter)
    : AuthRequest<PaginatedList<QuestionnaireResponse>>;

public sealed record QuestionnaireResponse(
    string Id,
    string Title,
    string LocationName,
    string OrganizationName,
    string Classification,
    string Type,
    string Placement,
    bool IsFromLocation);

internal sealed class BrowseQuestionnairesAuth : IAuth<BrowseQuestionnairesQuery, PaginatedList<QuestionnaireResponse>>
{
    public BrowseQuestionnairesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class
    BrowseQuestionnairesHandler : IRequestHandler<BrowseQuestionnairesQuery, PaginatedList<QuestionnaireResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public BrowseQuestionnairesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<QuestionnaireResponse>> Handle(BrowseQuestionnairesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Questionnaires_ByLocation.Entry> dbQuery = session
            .Query<Questionnaires_ByLocation.Entry, Questionnaires_ByLocation>()
            .ProjectInto<Questionnaires_ByLocation.Entry>()
            .Where(x => x.IsActive);

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery.Search(x => x.SearchParams, $"{query.PagedSearchParams.SearchParam}*");
        }

        dbQuery = query.LocationFilter switch
        {
            "organization" => dbQuery.Where(x => x.OrganizationId == _user.SelectedOrganizationId),
            "location" => dbQuery.Where(x => x.LocationId == _user.SelectedLocationId),
            "general" => dbQuery.Where(x => x.OrganizationId == null),
            "other" => dbQuery.Where(x => x.OrganizationId != _user.SelectedOrganizationId),
            _ => throw new ArgumentOutOfRangeException()
        };

        var queryItems = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<QuestionnaireResponse>.Create(
            queryItems.Select(q =>
                new QuestionnaireResponse(q.QuestionnaireId, q.Title, q.LocationName ?? "General",
                    q.OrganizationName ?? "General", q.Classification, q.Type, q.Placement,
                    q.LocationId == _user.SelectedLocationId)),
            stats.TotalResults, query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize);
    }
}