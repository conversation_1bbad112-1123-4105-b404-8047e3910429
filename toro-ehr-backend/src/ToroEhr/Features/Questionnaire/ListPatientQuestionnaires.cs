using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Features.Questionnaire.Shared;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Questionnaire;

public sealed record ListPatientQuestionnairesQuery(string Placement, string? EncounterId)
    : AuthRequest<IEnumerable<PatientQuestionnaireResponse>>;

public sealed record PatientQuestionnaireResponse(
    string Id,
    string Title,
    string LocationName,
    string OrganizationName,
    IEnumerable<QuestionResponse> Questions);

internal sealed class
    ListPatientQuestionnairesAuth : IAuth<ListPatientQuestionnairesQuery, IEnumerable<PatientQuestionnaireResponse>>
{
    public ListPatientQuestionnairesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class
    ListPatientQuestionnairesHandler : IRequestHandler<ListPatientQuestionnairesQuery,
    IEnumerable<PatientQuestionnaireResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPatientQuestionnairesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<IEnumerable<PatientQuestionnaireResponse>> Handle(ListPatientQuestionnairesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        string patientId;
        if (_user.IsPatient)
        {
            patientId = _user.PatientId!;
        }
        else
        {
            var encounter = await session.LoadAsync<Domain.Encounter>(query.EncounterId, cancellationToken);
            patientId = encounter.PatientId;
        }

        var patientOrganizations = await session.Query<OrganizationPatient>().Where(x => x.PatientId == patientId)
            .ToListAsync(token: cancellationToken);

        // get questionnaires for patient's organizations
        var allQuestionnaires = await session
            .Query<Questionnaires_ByLocation.Entry, Questionnaires_ByLocation>()
            .ProjectInto<Questionnaires_ByLocation.Entry>()
            .Include(x => x.QuestionnaireId)
            .Where(x => x.OrganizationId.In(patientOrganizations.Select(po => po.OrganizationId).ToList()) &&
                        x.Placement == query.Placement && x.IsActive)
            .ToListAsync(token: cancellationToken);

        var result = new List<PatientQuestionnaireResponse>();

        var responses = await session.Query<Domain.QuestionnaireResponse>().Where(x =>
            x.QuestionnaireId.In(allQuestionnaires.Select(lq => lq.QuestionnaireId)) &&
            x.PatientId == _user.PatientId).ToListAsync(token: cancellationToken);

        foreach (var entry in allQuestionnaires)
        {
            var questionnaire = await session.LoadAsync<Domain.Questionnaire>(entry.QuestionnaireId, cancellationToken);

            var questionnaireResponse = responses.FirstOrDefault(x => x.QuestionnaireId == entry.QuestionnaireId);

            // get answers with migration from previous versions for unmodified questions
            var questionAnswers = await GetQuestionAnswersWithMigration(session, questionnaire, questionnaireResponse, cancellationToken);

            result.Add(new PatientQuestionnaireResponse
            (
                entry.QuestionnaireId,
                entry.Title,
                entry.LocationName,
                entry.OrganizationName,
                questionAnswers));
        }

        return result;
    }

    private async Task<IEnumerable<QuestionResponse>> GetQuestionAnswersWithMigration(
        IAsyncDocumentSession session,
        Domain.Questionnaire currentQuestionnaire,
        Domain.QuestionnaireResponse? currentResponse,
        CancellationToken cancellationToken)
    {
        var questionAnswers = new List<QuestionResponse>();

        // if no response exists for current version, try to migrate from previous versions
        if (currentResponse == null || currentResponse.QuestionnaireVersion < currentQuestionnaire.Version)
        {
            // get all previous responses for this questionnaire
            var allResponses = await session.Query<Domain.QuestionnaireResponse>()
                .Where(x => x.QuestionnaireId == currentQuestionnaire.QuestionnaireId &&
                           x.PatientId == _user.PatientId)
                .OrderByDescending(x => x.QuestionnaireVersion)
                .ToListAsync(cancellationToken);

            // get all versions of this questionnaire to understand question evolution
            var allVersions = await session.Query<Domain.Questionnaire>()
                .Where(x => x.QuestionnaireId == currentQuestionnaire.QuestionnaireId)
                .OrderBy(x => x.Version)
                .ToListAsync(cancellationToken);

            foreach (var question in currentQuestionnaire.Questions)
            {
                var answerValues = new List<string>();

                // first check current response
                if (currentResponse != null)
                {
                    var currentAnswer = currentResponse.Answers.FirstOrDefault(a => a.QuestionId == question.Id);
                    if (currentAnswer != null)
                    {
                        answerValues = currentAnswer.Values;
                    }
                }

                // if no answer in current response, try to find from previous versions
                if (!answerValues.Any())
                {
                    answerValues = FindAnswerFromPreviousVersions(question, allResponses, allVersions);
                }

                questionAnswers.Add(new QuestionResponse(
                    question.Id,
                    question.Text,
                    question.Type,
                    question.IsRequired,
                    question.Options,
                    answerValues));
            }
        }
        else
        {
            // use current response as-is
            foreach (var question in currentQuestionnaire.Questions)
            {
                var answerValues = currentResponse.Answers
                    .FirstOrDefault(a => a.QuestionId == question.Id)?.Values ?? [];

                questionAnswers.Add(new QuestionResponse(
                    question.Id,
                    question.Text,
                    question.Type,
                    question.IsRequired,
                    question.Options,
                    answerValues));
            }
        }

        return questionAnswers;
    }

    private List<string> FindAnswerFromPreviousVersions(
        Question currentQuestion,
        List<Domain.QuestionnaireResponse> allResponses,
        List<Domain.Questionnaire> allVersions)
    {
        // look through previous versions to find an unmodified question
        foreach (var response in allResponses)
        {
            var responseVersion = allVersions.FirstOrDefault(v => v.Version == response.QuestionnaireVersion);
            if (responseVersion == null) continue;

            var previousQuestion = responseVersion.Questions.FirstOrDefault(q => q.Id == currentQuestion.Id);
            if (previousQuestion == null) continue;

            // check if question is essentially the same (unmodified)
            if (IsQuestionUnmodified(previousQuestion, currentQuestion))
            {
                var answer = response.Answers.FirstOrDefault(a => a.QuestionId == currentQuestion.Id);
                if (answer != null && answer.Values.Any())
                {
                    return answer.Values;
                }
            }
        }

        return new List<string>();
    }

    private bool IsQuestionUnmodified(Question previousQuestion, Question currentQuestion)
    {
        return previousQuestion.Text == currentQuestion.Text &&
               previousQuestion.Type == currentQuestion.Type &&
               previousQuestion.IsRequired == currentQuestion.IsRequired &&
               previousQuestion.Options.SequenceEqual(currentQuestion.Options);
    }
}