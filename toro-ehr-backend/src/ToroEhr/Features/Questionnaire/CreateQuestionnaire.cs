using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Features.Questionnaire.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Questionnaire;

public sealed record CreateQuestionnaireCommand(
    string Title,
    string Classification,
    string Type,
    string Placement,
    IEnumerable<QuestionRequest> Questions) : AuthRequest<Unit>;

internal sealed class CreateQuestionnaireAuth : IAuth<CreateQuestionnaireCommand, Unit>
{
    public CreateQuestionnaireAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public sealed class CreateQuestionnaireCommandValidator : AbstractValidator<CreateQuestionnaireCommand>
{
    public CreateQuestionnaireCommandValidator()
    {
        RuleFor(x => x.Title).NotEmpty();
        RuleFor(x => x.Classification).NotEmpty();
        RuleFor(x => x.Type).NotEmpty();
        RuleFor(x => x.Placement).NotEmpty();
        RuleFor(x => x.Questions)
            .NotEmpty().WithMessage("At least one question is required.");

        RuleForEach(x => x.Questions).ChildRules(question =>
        {
            question.RuleFor(q => q.Text).NotEmpty();

            question.RuleFor(q => q.Options)
                .NotEmpty()
                .When(q => !string.Equals(q.Type, "FreeText", StringComparison.OrdinalIgnoreCase))
                .WithMessage("Options must be provided for Single Choice and Multiple Choice questions.");
        });


    }
}

internal sealed class CreateQuestionnaireHandler : IRequestHandler<CreateQuestionnaireCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateQuestionnaireHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(CreateQuestionnaireCommand command, CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        string locationId = _user.SelectedLocationId!;

        Domain.Questionnaire questionnaire = Domain.Questionnaire.Create(locationId, command.Title,
            command.Classification,
            command.Type,
            command.Placement,
            command.Questions.Select(q =>
                    new Question(Utils.GenerateRandomId(), q.Text, q.Type, q.IsRequired, q.Options ?? []))
                .ToList(),
            command.Timestamp);

        await session.StoreAsync(questionnaire, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}