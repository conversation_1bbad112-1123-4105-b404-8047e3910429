using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Features.Questionnaire.Shared;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;
using Utils = ToroEhr.Shared.Utils;

namespace ToroEhr.Features.Questionnaire;

public sealed record UpdateQuestionnaireCommand(
    string Id,
    string Title,
    string Classification,
    string Placement,
    IEnumerable<QuestionRequest> Questions) : AuthRequest<Unit>;

internal sealed class UpdateQuestionnaireAuth : IAuth<UpdateQuestionnaireCommand, Unit>
{
    public UpdateQuestionnaireAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public sealed class UpdateQuestionnaireCommandValidator : AbstractValidator<UpdateQuestionnaireCommand>
{
    public UpdateQuestionnaireCommandValidator()
    {
        RuleFor(x => x.Title).NotEmpty();
        RuleFor(x => x.Classification).NotEmpty();
        RuleFor(x => x.Placement).NotEmpty();
        RuleFor(x => x.Questions)
            .NotEmpty().WithMessage("At least one question is required.");

        RuleForEach(x => x.Questions).ChildRules(question =>
        {
            question.RuleFor(q => q.Text).NotEmpty();

            question.RuleFor(q => q.Options)
                .NotEmpty()
                .When(q => !string.Equals(q.Type, "FreeText", StringComparison.OrdinalIgnoreCase))
                .WithMessage("Options must be provided for Single Choice and Multiple Choice questions.");
        });
    }
}

internal sealed class UpdateQuestionnaireHandler : IRequestHandler<UpdateQuestionnaireCommand, Unit>
{
    private readonly IDocumentStore _store;

    public UpdateQuestionnaireHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
    }

    public async Task<Unit> Handle(UpdateQuestionnaireCommand command, CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        Domain.Questionnaire currentQuestionnaire =
            await session.LoadAsync<Domain.Questionnaire>(command.Id, cancellationToken);

        Guard.AgainstNotFound(currentQuestionnaire,
            new AppError("Questionnaire.NotFound", $"Questionnaire with 'id:' {command.Id} not found!"));

        var newQuestions = command.Questions.Select(q =>
            new Question(q.Id.IsNullOrWhiteSpace() ? Utils.GenerateRandomId() : q.Id, q.Text, q.Type, q.IsRequired,
                q.Options ?? [])).ToList();

        // check if required questions changed
        bool hasRequiredQuestionsChanged = currentQuestionnaire.HasRequiredQuestionsChanged(newQuestions);

        // create new version
        currentQuestionnaire.MarkAsNotLatest();

        var newVersion = Domain.Questionnaire.CreateNewVersion(
            currentQuestionnaire,
            command.Title,
            command.Classification,
            command.Placement,
            newQuestions,
            command.Timestamp);

        await session.StoreAsync(currentQuestionnaire, cancellationToken);
        await session.StoreAsync(newVersion, cancellationToken);

        // send notifications if required questions changed and it's a Profile questionnaire
        if (hasRequiredQuestionsChanged && string.Equals(command.Placement, "Profile", StringComparison.OrdinalIgnoreCase))
        {
            await SendQuestionnaireUpdateNotifications(session, newVersion, command.Timestamp, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }

    private async Task SendQuestionnaireUpdateNotifications(IAsyncDocumentSession session,
        Domain.Questionnaire questionnaire, DateTime timestamp, CancellationToken cancellationToken)
    {
        // get all patients who have access to this questionnaire
        List<string> patientIds;

        if (questionnaire.LocationId == null)
        {
            // general questionnaire - notify all patients in the system
            patientIds = await session.Query<Domain.Patient>()
                .Select(p => p.Id)
                .ToListAsync(cancellationToken);
        }
        else
        {
            // location-specific questionnaire - notify patients in that organization
            var location = await session.LoadAsync<Domain.Location>(questionnaire.LocationId, cancellationToken);
            patientIds = await session.Query<OrganizationPatient>()
                .Where(op => op.OrganizationId == location.OrganizationId)
                .Select(op => op.PatientId)
                .ToListAsync(cancellationToken);
        }

        // create notifications for all patients
        foreach (var patientId in patientIds)
        {
            await NotificationService.CreateQuestionnaireUpdatedNotification(
                session, patientId, questionnaire.Title, questionnaire.Id, timestamp);
        }
    }
}