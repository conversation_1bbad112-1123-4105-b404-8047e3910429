using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Exceptions;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Questionnaire;

public sealed record DeactivateQuestionnaireCommand(string Id) : AuthRequest<Unit>;

internal sealed class DeactivateQuestionnaireAuth : IAuth<DeactivateQuestionnaireCommand, Unit>
{
    public DeactivateQuestionnaireAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        
        // both super admin and regular employees can deactivate questionnaires
        // business logic will ensure they can only deactivate questionnaires they have access to
    }
}

internal sealed class DeactivateQuestionnaireHandler : IRequestHandler<DeactivateQuestionnaireCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly Authenticator _user;

    public DeactivateQuestionnaireHandler(IDocumentStore store, Authenticator user)
    {
        _store = store;
        _user = user;
    }

    public async Task<Unit> Handle(DeactivateQuestionnaireCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // try to load by document ID first
        Domain.Questionnaire questionnaire = await session.LoadAsync<Domain.Questionnaire>(command.Id, cancellationToken);

        string questionnaireId;
        if (questionnaire != null)
        {
            questionnaireId = questionnaire.QuestionnaireId;
        }
        else
        {
            // try to find by QuestionnaireId
            questionnaire = await session.Query<Domain.Questionnaire>()
                .Where(x => x.QuestionnaireId == command.Id && x.IsLatest)
                .FirstOrDefaultAsync(cancellationToken);

            Guard.AgainstNotFound(questionnaire,
                new AppError("Questionnaire.NotFound", $"Questionnaire with 'id:' {command.Id} not found!"));

            questionnaireId = questionnaire.QuestionnaireId;
        }

        // check if this is a VisitReason questionnaire and if it's the only one
        if (questionnaire.Type == QuestionnaireType.VisitReason.Name)
        {
            await ValidateNotLastVisitReasonQuestionnaire(session, questionnaire, cancellationToken);
        }

        // deactivate all versions of this questionnaire
        var allVersions = await session.Query<Domain.Questionnaire>()
            .Where(x => x.QuestionnaireId == questionnaireId)
            .ToListAsync(cancellationToken);

        foreach (var version in allVersions)
        {
            version.Deactivate();
            await session.StoreAsync(version, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }

    private async Task ValidateNotLastVisitReasonQuestionnaire(IAsyncDocumentSession session,
        Domain.Questionnaire questionnaire, CancellationToken cancellationToken)
    {
        // count active VisitReason questionnaires in the same location
        var activeVisitReasonCount = await session.Query<Domain.Questionnaire>()
            .Where(x => x.LocationId == questionnaire.LocationId &&
                       x.Type == QuestionnaireType.VisitReason.Name &&
                       x.IsActive &&
                       x.IsLatest)
            .CountAsync(cancellationToken);

        if (activeVisitReasonCount <= 1)
        {
            throw new ValidationException([
                new AppError("Questionnaire.CannotDeactivateLastVisitReason",
                    "Cannot deactivate the last 'Visit Reason' questionnaire. At least one 'Visit Reason' questionnaire must remain active.")
            ]);
        }
    }
}
