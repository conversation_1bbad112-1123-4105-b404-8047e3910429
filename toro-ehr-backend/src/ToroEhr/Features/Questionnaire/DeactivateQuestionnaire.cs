using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Questionnaire;

public sealed record DeactivateQuestionnaireCommand(string Id) : AuthRequest<Unit>;

internal sealed class DeactivateQuestionnaireAuth : IAuth<DeactivateQuestionnaireCommand, Unit>
{
    public DeactivateQuestionnaireAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        
        // both super admin and regular employees can deactivate questionnaires
        // business logic will ensure they can only deactivate questionnaires they have access to
    }
}

internal sealed class DeactivateQuestionnaireHandler : IRequestHandler<DeactivateQuestionnaireCommand, Unit>
{
    private readonly IDocumentStore _store;

    public DeactivateQuestionnaireHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(DeactivateQuestionnaireCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // try to load by document ID first
        Domain.Questionnaire questionnaire = await session.LoadAsync<Domain.Questionnaire>(command.Id, cancellationToken);

        string questionnaireId;
        if (questionnaire != null)
        {
            questionnaireId = questionnaire.QuestionnaireId;
        }
        else
        {
            // try to find by QuestionnaireId
            questionnaire = await session.Query<Domain.Questionnaire>()
                .Where(x => x.QuestionnaireId == command.Id && x.IsLatest)
                .FirstOrDefaultAsync(cancellationToken);

            Guard.AgainstNotFound(questionnaire,
                new AppError("Questionnaire.NotFound", $"Questionnaire with 'id:' {command.Id} not found!"));

            questionnaireId = questionnaire.QuestionnaireId;
        }

        // deactivate all versions of this questionnaire
        var allVersions = await session.Query<Domain.Questionnaire>()
            .Where(x => x.QuestionnaireId == questionnaireId)
            .ToListAsync(cancellationToken);

        foreach (var version in allVersions)
        {
            version.Deactivate();
            await session.StoreAsync(version, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}
