using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Questionnaire;

public sealed record SaveQuestionnaireResponseCommand(
    string QuestionnaireId,
    string? EncounterId,
    IEnumerable<QuestionAnswer> Answers) : AuthRequest<Unit>;

public record QuestionAnswer(string QuestionId, List<string> Answers);

internal sealed class SaveQuestionnaireResponseAuth : IAuth<SaveQuestionnaireResponseCommand, Unit>
{
    public SaveQuestionnaireResponseAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public sealed class SaveQuestionnaireResponseCommandValidator : AbstractValidator<SaveQuestionnaireResponseCommand>
{
    public SaveQuestionnaireResponseCommandValidator()
    {
        RuleFor(x => x.QuestionnaireId).NotEmpty();
    }
}

internal sealed class SaveQuestionnaireResponseHandler : IRequestHandler<SaveQuestionnaireResponseCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public SaveQuestionnaireResponseHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(SaveQuestionnaireResponseCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var patientId = await GetPatientIdAsync(session, command, cancellationToken);

        // get the latest questionnaire version
        var latestQuestionnaire = await session.Query<Domain.Questionnaire>()
            .Where(x => x.Id == command.QuestionnaireId || x.QuestionnaireId == command.QuestionnaireId)
            .Where(x => x.IsLatest)
            .FirstOrDefaultAsync(cancellationToken);

        Guard.AgainstNotFound(latestQuestionnaire,
            new AppError("Questionnaire.NotFound", $"Questionnaire with 'id:' {command.QuestionnaireId} not found!"));

        var questionnaireResponse = await GetOrCreateResponseAsync(session, command, patientId, latestQuestionnaire, cancellationToken);

        await session.StoreAsync(questionnaireResponse, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }

    private async Task<string> GetPatientIdAsync(IAsyncDocumentSession session, SaveQuestionnaireResponseCommand command, CancellationToken cancellationToken)
    {
        if (_user.IsPatient)
        {
            return _user.PatientId!;
        }

        var encounter = await session.LoadAsync<Domain.Encounter>(command.EncounterId, cancellationToken);
        return encounter.PatientId;
    }

    private async Task<Domain.QuestionnaireResponse> GetOrCreateResponseAsync(IAsyncDocumentSession session,
        SaveQuestionnaireResponseCommand command, string patientId, Domain.Questionnaire latestQuestionnaire, CancellationToken cancellationToken)
    {
        var questionnaireResponse =
            command.EncounterId is null
                ? await FindProfileQuestionnaireResponse(session, command, cancellationToken)
                : await FindCheckInQuestionnaireResponse(session, command, cancellationToken);

        if (questionnaireResponse is null)
        {
            return Domain.QuestionnaireResponse.Create(patientId, latestQuestionnaire.QuestionnaireId,
                latestQuestionnaire.Version, command.EncounterId, command.Timestamp,
                command.Answers.Select(x => new Answer(x.QuestionId, x.Answers)).ToList());
        }

        // check if response needs to be migrated to latest version
        if (questionnaireResponse.QuestionnaireVersion < latestQuestionnaire.Version)
        {
            // migrate existing answers to new version
            var migratedAnswers = MigrateAnswersToNewVersion(questionnaireResponse.Answers, latestQuestionnaire.Questions, command.Answers);
            questionnaireResponse.UpdateToNewVersion(latestQuestionnaire.Version, migratedAnswers, command.Timestamp);
        }
        else
        {
            questionnaireResponse.Update(command.Answers, command.Timestamp);
        }

        return questionnaireResponse;
    }

    private IEnumerable<QuestionAnswer> MigrateAnswersToNewVersion(List<Answer> existingAnswers,
        List<Question> newQuestions, IEnumerable<QuestionAnswer> newAnswers)
    {
        var result = new List<QuestionAnswer>();
        var newAnswersList = newAnswers.ToList();

        // for each question in the new version
        foreach (var question in newQuestions)
        {
            // check if there's a new answer for this question
            var newAnswer = newAnswersList.FirstOrDefault(na => na.QuestionId == question.Id);
            if (newAnswer != null)
            {
                result.Add(newAnswer);
                continue;
            }

            // check if there's an existing answer for this question
            var existingAnswer = existingAnswers.FirstOrDefault(ea => ea.QuestionId == question.Id);
            if (existingAnswer != null)
            {
                result.Add(new QuestionAnswer(question.Id, existingAnswer.Values));
            }
            else
            {
                // new question with no existing answer - add empty answer
                result.Add(new QuestionAnswer(question.Id, new List<string>()));
            }
        }

        return result;
    }

    private async Task<Domain.QuestionnaireResponse?> FindProfileQuestionnaireResponse(IAsyncDocumentSession session,
        SaveQuestionnaireResponseCommand command, CancellationToken cancellationToken) =>
        await session.Query<Domain.QuestionnaireResponse>()
            .Where(x => x.QuestionnaireId == command.QuestionnaireId &&
                        x.PatientId == _user.PatientId &&
                        x.EncounterId == null)
            .FirstOrDefaultAsync(cancellationToken);

    private async Task<Domain.QuestionnaireResponse?> FindCheckInQuestionnaireResponse(IAsyncDocumentSession session,
        SaveQuestionnaireResponseCommand command, CancellationToken cancellationToken) =>
        await session.Query<Domain.QuestionnaireResponse>()
            .Where(x => x.QuestionnaireId == command.QuestionnaireId &&
                        x.EncounterId == command.EncounterId)
            .FirstOrDefaultAsync(cancellationToken);
}