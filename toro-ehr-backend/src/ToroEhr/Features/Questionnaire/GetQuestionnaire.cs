using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Features.Questionnaire.Shared;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Questionnaire;

public sealed record GetQuestionnaireQuery(string Id) : AuthRequest<QuestionnaireDetailsResponse>;

public sealed record QuestionnaireDetailsResponse(
    string Id,
    string Title,
    string Classification,
    QuestionnaireType Type,
    string Placement,
    IEnumerable<QuestionResponse> Questions);

internal sealed class GetQuestionnaireAuth : IAuth<GetQuestionnaireQuery, QuestionnaireDetailsResponse>
{
    public GetQuestionnaireAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class
    GetQuestionnaireHandler : IRequestHandler<GetQuestionnaireQuery, QuestionnaireDetailsResponse>
{
    private readonly IDocumentStore _store;

    public GetQuestionnaireHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<QuestionnaireDetailsResponse> Handle(GetQuestionnaireQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // try to load by document ID first, then by QuestionnaireId (for latest version)
        Domain.Questionnaire questionnaire = await session.LoadAsync<Domain.Questionnaire>(query.Id, cancellationToken);

        if (questionnaire == null)
        {
            // try to find latest version by QuestionnaireId
            questionnaire = await session.Query<Domain.Questionnaire>()
                .Where(x => x.QuestionnaireId == query.Id && x.IsLatest)
                .FirstOrDefaultAsync(cancellationToken);
        }

        Guard.AgainstNotFound(questionnaire,
            new AppError("Questionnaire.NotFound", $"Questionnaire with 'id:' {query.Id} not found!"));

        return new QuestionnaireDetailsResponse(
            questionnaire.Id,
            questionnaire.Title,
            questionnaire.Classification,
            QuestionnaireType.FromName(questionnaire.Type),
            questionnaire.Placement,
            questionnaire.Questions.Select(q =>
                new QuestionResponse(q.Id, q.Text, q.Type, q.IsRequired, q.Options, [])));
    }
}