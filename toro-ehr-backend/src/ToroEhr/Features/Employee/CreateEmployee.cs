using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Features.Authentication.Events;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace ToroEhr.Features.Employee;

public sealed record CreateEmployeeCommand(
    string OrganizationId,
    string LocationId,
    List<string> Roles,
    string Email,
    string FirstName,
    string LastName,
    string Npi,
    string PhoneNumber,
    string TimeZone,
    string CalendarColor,
    AddressRequest Address) : AuthRequest<string>;

internal sealed class CreateEmployeeCommandValidator : AbstractValidator<CreateEmployeeCommand>
{
    public CreateEmployeeCommandValidator()
    {
        RuleFor(x => x.OrganizationId).NotEmpty();
        RuleFor(x => x.Roles).NotEmpty();
        RuleFor(x => x.Email).NotEmpty();
        RuleFor(x => x.FirstName).NotEmpty();
        RuleFor(x => x.LastName).NotEmpty();
        RuleFor(x => x.Npi).NotEmpty();
        RuleFor(x => x.PhoneNumber).NotEmpty();
        RuleFor(x => x.TimeZone).NotEmpty();
        RuleFor(x => x.CalendarColor).NotEmpty();
        RuleFor(x => x.Address).SetValidator(new AddressRequestValidator());
    }
}

internal sealed class CreateEmployeeAuth : IAuth<CreateEmployeeCommand, string>
{
    public CreateEmployeeAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.OrganizationAdmin, EmployeeRole.LocationAdmin]);
    }
}

internal sealed class CreateEmployeeHandler : IRequestHandler<CreateEmployeeCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateEmployeeHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateEmployeeCommand request, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Address address = Domain.Address.Create(request.Address.Street, request.Address.City,
            request.Address.State, request.Address.ZipCode);

        Domain.Employee employee = Domain.Employee.Create(request.Email, request.FirstName,
            request.LastName, request.Npi, request.PhoneNumber, address);

        Domain.LocationEmployee organizationEmployee =
            Domain.LocationEmployee.Create(request.OrganizationId, request.LocationId, employee.Id, request.Roles,
                request.CalendarColor, request.TimeZone, Utils.GenerateRandomId(), request.Timestamp, false, null);

        Domain.User existingUser = await session.Query<Domain.User>()
            .FirstOrDefaultAsync(x => x.PrimaryEmail == request.Email, token: cancellationToken);

        if (existingUser is null)
        {
            Domain.User newUser =
                Domain.User.Create(request.Email, request.FirstName, request.LastName, null, employee.Id,
                    [UserRole.Employee.Name]);
            await session.StoreAsync(newUser, cancellationToken);
        }

        await session.StoreAsync(employee, cancellationToken);
        await session.StoreAsync(organizationEmployee, cancellationToken);
        await session.StoreAsync(
            new SendInvitationEmail(employee.FirstName, employee.LastName, employee.Email, _user,
                organizationEmployee.InvitationToken!, request.Roles), cancellationToken);
        
        await session.SaveChangesAsync(cancellationToken);

        return employee.Id;
    }
}