using MediatR;
using NodaTime;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.ValueObjects;

namespace ToroEhr.Features.Employee;

public sealed record GetAvailableAppointmentSlotsQuery(string EmployeeId, string LocationId, DateTimeOffset Date, bool Reschedule) : AuthRequest<AvailableAppointmentSlotsResponse>;

public sealed record AppointmentSlot(DateTimeOffset From, DateTimeOffset To, int DurationInMinutes, string PractitionerTimeZone);

public sealed record AvailableAppointmentSlotsResponse(
    List<AppointmentSlot> AvailableSlots,
    ExistingAppointmentInfo? ExistingAppointment);

public sealed record ExistingAppointmentInfo(
    string AppointmentId,
    DateTimeOffset StartAt,
    DateTimeOffset EndAt,
    string EmployeeName,
    string LocationName,
    string Status);

internal sealed class GetAvailableAppointmentSlotsAuth : IAuth<GetAvailableAppointmentSlotsQuery, AvailableAppointmentSlotsResponse>
{
    public GetAvailableAppointmentSlotsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class
    GetAvailableAppointmentSlotsHandler : IRequestHandler<GetAvailableAppointmentSlotsQuery, AvailableAppointmentSlotsResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetAvailableAppointmentSlotsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<AvailableAppointmentSlotsResponse> Handle(GetAvailableAppointmentSlotsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var organizationEmployee = await session
            .Query<Domain.LocationEmployee>()
            .Include(x => x.LocationId)
            .Where(x => x.LocationId == query.LocationId && x.EmployeeId == query.EmployeeId)
            .FirstOrDefaultAsync(cancellationToken);

        if (query.Date.Date == DateTimeOffset.Now.Date)
        {
            var location = await session.LoadAsync<Domain.Location>(organizationEmployee.LocationId, cancellationToken);
            if (!location.SameDayAppointment)
            {
                return new AvailableAppointmentSlotsResponse(new List<AppointmentSlot>(), null);
            }
        }

        Guard.AgainstNotFound(organizationEmployee,
            new("OrganizationEmployee.NotFound", $"Employee with 'id:' {query.EmployeeId} and " +
            $"Location 'id:' {query.LocationId} not found!"));

        var officeHours = organizationEmployee.OfficeHours.FirstOrDefault(x => x.Day == query.Date.DayOfWeek);
        if (officeHours == null || officeHours.OpenTime == null || officeHours.CloseTime == null)
        {
            return new AvailableAppointmentSlotsResponse(new List<AppointmentSlot>(), null);
        }

        if (!query.Reschedule)
        {
            // check if patient already has an appointment on this date
            var existingPatientAppointment = await session
                .Query<Domain.Appointment>()
                .Include(x => x.EmployeeId)
                .Include(x => x.LocationId)
                .Where(x => x.PatientId == _user.PatientId &&
                           x.EmployeeId == query.EmployeeId &&
                           x.StartAt.Date == query.Date.Date &&
                           x.Status != AppointmentStatus.Canceled.Name &&
                           x.Status != AppointmentStatus.CanceledLate.Name &&
                           x.Status != AppointmentStatus.Missed.Name)
                .FirstOrDefaultAsync(cancellationToken);

            // if patient has existing appointment, return empty slots immediately
            if (existingPatientAppointment != null)
            {
                var employee = await session.LoadAsync<Domain.Employee>(existingPatientAppointment.EmployeeId, cancellationToken);
                var location = await session.LoadAsync<Domain.Location>(existingPatientAppointment.LocationId, cancellationToken);

                var existingAppointmentInfo = new ExistingAppointmentInfo(
                    existingPatientAppointment.Id,
                    existingPatientAppointment.StartAt,
                    existingPatientAppointment.EndAt,
                    $"{employee.FirstName} {employee.LastName}",
                    location.Name,
                    existingPatientAppointment.Status);

                return new AvailableAppointmentSlotsResponse(new List<AppointmentSlot>(), existingAppointmentInfo);
            }

        }

        // no existing appointment, calculate available slots
        var appointments = await session
            .Query<Domain.Appointment>()
            .Where(x => x.EmployeeId == query.EmployeeId && x.StartAt > query.Date.Date && x.EndAt < query.Date.AddDays(1).Date)
            .ToListAsync(cancellationToken);

        var availableSlots = CalculateAvailableTimeSlots(query.Date, officeHours, organizationEmployee.OutOfOfficeHours, appointments,
            organizationEmployee.NumberOfAppointmentOverlaps, organizationEmployee.AppointmentDurationInMinutes, organizationEmployee.TimeZone);

        return new AvailableAppointmentSlotsResponse(availableSlots, null);
    }

    private static List<AppointmentSlot> CalculateAvailableTimeSlots(
        DateTimeOffset date,
        OfficeHours officeHours,
        List<OutOfOfficeHours> outOfOfficeHours,
        List<Domain.Appointment> bookedAppointments,
        int maxOverlappingAppointments,
        int durationInMinutes,
        string timeZoneId)
    {
        var tzProvider = DateTimeZoneProviders.Tzdb;
        var timeZone = tzProvider[timeZoneId];

        if (timeZone == null)
            throw new ArgumentException($"Invalid time zone: {timeZoneId}");

        // Get current instant in the target timezone
        var nowInstant = SystemClock.Instance.GetCurrentInstant();
        var zonedNow = nowInstant.InZone(timeZone);
        var todayDate = zonedNow.Date; // NodaTime.LocalDate representing today in timezone

        // Convert input date to LocalDate in timezone
        var targetLocalDate = LocalDate.FromDateTime(date.UtcDateTime);

        // Determine start time for slots
        TimeSpan startTime;

        if (targetLocalDate == todayDate)
        {
            // Current time as TimeSpan
            var currentTimeSpan = TimeSpan.FromTicks(zonedNow.TimeOfDay.TickOfDay);

            // Round up to next duration increment
            var minutesPastIncrement = currentTimeSpan.Minutes % durationInMinutes;
            var minutesToAdd = minutesPastIncrement == 0 ? 0 : durationInMinutes - minutesPastIncrement;
            var roundedTimeSpan = currentTimeSpan.Add(TimeSpan.FromMinutes(minutesToAdd));

            // Ensure not before opening
            startTime = (TimeSpan)(roundedTimeSpan < officeHours.OpenTime ? officeHours.OpenTime : roundedTimeSpan);
        }
        else
        {
            // Future date: use office opening time
            startTime = (TimeSpan)officeHours.OpenTime!;
        }

        // Use the timezone offset to create DateTimeOffset base
        var offset = zonedNow.Offset;
        var baseDateTimeOffset = new DateTimeOffset(date.Date, offset.ToTimeSpan());

        var availableSlots = new List<AppointmentSlot>();
        var slotDuration = TimeSpan.FromMinutes(durationInMinutes);

        DateTimeOffset currentSlotStart = baseDateTimeOffset.Add(startTime);
        DateTimeOffset endOfDay = baseDateTimeOffset.Add(officeHours.CloseTime!.Value);

        while (currentSlotStart < endOfDay)
        {
            var slotEnd = currentSlotStart + slotDuration;

            // Skip if overlaps out of office hours
            var overlappingOutOfOffice = outOfOfficeHours
                .FirstOrDefault(x => IsOverlapping(currentSlotStart, slotEnd, x.StartAt, x.EndAt));
            if (overlappingOutOfOffice != null)
            {
                currentSlotStart = overlappingOutOfOffice.EndAt;
                continue;
            }

            // Skip if overlaps office exclusions
            var overlappingExclusion = officeHours.Exclusions
                .FirstOrDefault(e => IsOverlapping(
                    currentSlotStart,
                    slotEnd,
                    baseDateTimeOffset.Add(e.From),
                    baseDateTimeOffset.Add(e.To))
                );
            if (overlappingExclusion != null)
            {
                currentSlotStart = baseDateTimeOffset.Add(overlappingExclusion.To);
                continue;
            }

            // Count overlapping booked appointments
            int overlappingCount = bookedAppointments.Count(b =>
                IsOverlapping(currentSlotStart, slotEnd, b.StartAt, b.EndAt));

            if (overlappingCount <= maxOverlappingAppointments)
            {
                availableSlots.Add(new AppointmentSlot(currentSlotStart, slotEnd, durationInMinutes, timeZoneId));
            }

            currentSlotStart = slotEnd;
        }

        return availableSlots;
    }

    private static bool IsOverlapping(DateTimeOffset start1, DateTimeOffset end1, DateTimeOffset start2, DateTimeOffset end2)
    {
        return start1 < end2 && end1 > start2;
    }
}
