using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.ValueObjects;

namespace ToroEhr.Features.Employee;

public sealed record GetLocationEmployeeQuery(string LocationId, string EmployeeId) : AuthRequest<LocationEmployeeResponse>;

public sealed record LocationEmployeeResponse(
        string CalendarColor,
        int NumberOfAppointmentOverlaps,
        int AppointmentDurationInMinutes,
        bool PendingApprovalAppointments,
        string? SpecialtyClassification,
        List<string> ReceivedNotificationPreferences,
        List<OfficeHours> OfficeHours,
        List<OutOfOfficeHours> OutOfOfficeHours);

internal class GetLocationEmployeeAuth : IAuth<GetLocationEmployeeQuery, LocationEmployeeResponse>
{
    public GetLocationEmployeeAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.Practitioner]);
    }
}

internal class GetLocationEmployeeHandler : IRequestHandler<GetLocationEmployeeQuery, LocationEmployeeResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetLocationEmployeeHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<LocationEmployeeResponse> Handle(GetLocationEmployeeQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var locationEmployee = await session
            .Query<Domain.LocationEmployee>()
            .Where(x => x.LocationId == query.LocationId && x.EmployeeId == query.EmployeeId)
            .FirstOrDefaultAsync(cancellationToken);

        Guard.AgainstNotFound(locationEmployee, 
            new("LocationEmployee.NotFound", $"Employee with 'id:' {query.EmployeeId} and " +
            $"Location 'id:' {query.LocationId} not found!"));

        return new LocationEmployeeResponse(locationEmployee.CalendarColor,
            locationEmployee.NumberOfAppointmentOverlaps,
            locationEmployee.AppointmentDurationInMinutes,
            locationEmployee.PendingApprovalAppointments,
            locationEmployee.SpecialtyClassification,
            locationEmployee.ReceivedNotificationPreferences, 
            GetEveryDaysOfficeHours(locationEmployee.OfficeHours),
            locationEmployee.OutOfOfficeHours);
    }

    private static List<OfficeHours> GetEveryDaysOfficeHours(List<OfficeHours> officeHours)
    {
        var everyDays = Enum.GetValues<DayOfWeek>(); // Gets all 7 days
        var officeHoursDictionary = officeHours.ToDictionary(o => o.Day);

        return everyDays.Select(day =>
            officeHoursDictionary.TryGetValue(day, out var existing)
                ? existing
                : new OfficeHours(day, null, null, new List<Exclusion>())
        ).ToList();
    }
}