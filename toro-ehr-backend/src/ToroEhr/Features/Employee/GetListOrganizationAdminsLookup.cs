using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Employee;

public sealed record GetListOrganizationAdminsLookupQuery(string OrganizationId)
    : AuthRequest<List<SelectListItem>>;

internal sealed class GetListOrganizationAdminsLookupAuth : IAuth<GetListOrganizationAdminsLookupQuery, List<SelectListItem>>
{
    public GetListOrganizationAdminsLookupAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    GetListOrganizationAdminsLookupHandler : IRequestHandler<GetListOrganizationAdminsLookupQuery, List<SelectListItem>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetListOrganizationAdminsLookupHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<SelectListItem>> Handle(GetListOrganizationAdminsLookupQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        var employeeEntries = await session
            .Query<Employees_ByLocation.Entry, Employees_ByLocation>()
            .Where(x => x.OrganizationId == query.OrganizationId && x.Roles.Contains(EmployeeRole.OrganizationAdmin.Name))
            .ProjectInto<Employees_ByLocation.Entry>()
            .Include(x => x.EmployeeId)
            .ToListAsync(cancellationToken);

        var items = new List<SelectListItem>();
        foreach (var employeeId in employeeEntries.Select(x => x.EmployeeId).Distinct())
        {
            var employee = await session.LoadAsync<Domain.Employee>(employeeId);

            items.Add(new SelectListItem(employee.ShortName, employeeId));
        }

        return items;
    }
}
