using HandlebarsDotNet;
using MediatR;
using ToroEhr.Domain;
using ToroEhr.Features.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Templates.Email.SetPassword;

namespace ToroEhr.Features.Authentication.Events;

public record SendInvitationEmail(string RecipientFirstName, string RecipientLastName, string RecipientEmail,
        UserRequestSession User, string InvitationToken, List<string>? EmployeeRoles = null) : BaseEventEntity;

public class SendInvitationEmailEventHandler : INotificationHandler<SendInvitationEmail>
{
    private readonly EmailService _emailService;

    public SendInvitationEmailEventHandler(EmailService emailService)
    {
        _emailService = emailService;
    }
    public async Task Handle(SendInvitationEmail notification, CancellationToken cancellationToken = new())
    {
        var rootDir = Path.Combine("Templates", "Email", "SetPassword");
        var htmlTemplate = await File.ReadAllTextAsync(Path.Combine(rootDir, "index.html"), cancellationToken);
        var compiledTemplate = Handlebars.Compile(htmlTemplate);

        // determine email content based on employee roles or if it's a patient invitation
        string emailContent = GetEmailContent(notification);
        string actionUrl = notification.EmployeeRoles != null
            ? $"{Config.Application.Url}/set-password-employee?invitationToken={notification.InvitationToken}"
            : $"{Config.Application.Url}/set-password-patient?invitationToken={notification.InvitationToken}";

        string email = compiledTemplate(new SetPasswordEmail(Config.Application.Name, Config.Application.Url,
            $"{notification.RecipientFirstName} {notification.RecipientLastName}", notification.User.FullName, notification.User.OrganizationName!,
            actionUrl, emailContent));
        string subject = "Set up account";
        await _emailService.SendEmailAsync(notification.RecipientEmail, subject, email);
    }

    private string GetEmailContent(SendInvitationEmail notification)
    {
        // if no employee roles, it's a patient invitation
        if (notification.EmployeeRoles == null || !notification.EmployeeRoles.Any())
        {
            return $"{notification.User.FullName} with {notification.User.OrganizationName} has invited you to sign up for {Config.Application.Name} to manage and view your health record. Use the button below to set up your account and get started:";
        }

        // check if it's an organization admin (new organization)
        if (notification.EmployeeRoles.Contains("OrganizationAdmin") && string.IsNullOrEmpty(notification.User.OrganizationName))
        {
            return $"You are invited to sign up your organization with {Config.Application.Name}. Use the button below to set up your account and get started:";
        }

        // check if it's a location admin (new location)
        if (notification.EmployeeRoles.Contains("LocationAdmin"))
        {
            return $"{notification.User.FullName} with {notification.User.OrganizationName} has invited you to sign up your location with {Config.Application.Name}. Use the button below to set up your account and get started:";
        }

        // for practitioners, nurses, front desk
        return $"{notification.User.FullName} with {notification.User.OrganizationName} has invited you to sign up with {Config.Application.Name}. Use the button below to set up your account and get started:";
    }
}