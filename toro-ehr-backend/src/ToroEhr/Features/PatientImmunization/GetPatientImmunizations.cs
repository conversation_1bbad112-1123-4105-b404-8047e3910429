using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.PatientImmunization;

public sealed record GetPatientImmunizationQuery(string? PatientId) : AuthRequest<List<PatientImmunizationResponse>>;

public record PatientImmunizationResponse(string Id, string DisplayName, DateOnly Date);

internal sealed class
    GetPatientImmunizationsAuth : IAuth<GetPatientImmunizationQuery, List<PatientImmunizationResponse>>
{
    public GetPatientImmunizationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class
    GetPatientImmunizationsHandler : IRequestHandler<GetPatientImmunizationQuery, List<PatientImmunizationResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetPatientImmunizationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<PatientImmunizationResponse>> Handle(GetPatientImmunizationQuery command,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var patientId = command.PatientId ?? _user.PatientId;
        //todo: if query.PatientId not null add check for practitioner - patient relations

        IEnumerable<Domain.PatientImmunization> patientImmunizations = await session.Query<Domain.PatientImmunization>()
            .Where(x => x.PatientId == patientId).ToListAsync(token: cancellationToken);

        IEnumerable<Domain.OrderImmunization> immunizations = await session.Query<Domain.OrderImmunization>()
            .Where(x => x.PatientId == patientId && (x.Status == OrderStatus.Active.Name || x.Status == OrderStatus.Completed.Name))
            .ToListAsync(token: cancellationToken);

        var result = patientImmunizations.Select(x =>
                new PatientImmunizationResponse(x.Id, x.ImmunizationDisplayName, x.ImmunizationDate))
            .ToList();

        result.AddRange(immunizations.Select(x =>
                new PatientImmunizationResponse(x.Id, x.Name, DateOnly.FromDateTime(x.CompletedAt?.Date ?? DateTimeOffset.Now.Date)))
            .ToList());

        return result;
    }
}