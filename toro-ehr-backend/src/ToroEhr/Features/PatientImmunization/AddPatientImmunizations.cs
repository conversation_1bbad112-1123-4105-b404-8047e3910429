using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.PatientImmunization;

public sealed record AddPatientImmunizationCommand(string Code, DateOnly Date)
    : AuthRequest<Unit>;

internal sealed class SetPatientImmunizationsAuth : IAuth<AddPatientImmunizationCommand, Unit>
{
    public SetPatientImmunizationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class SetPatientImmunizationsCommandValidator : AbstractValidator<AddPatientImmunizationCommand>
{
    public SetPatientImmunizationsCommandValidator()
    {
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.Date).NotEmpty();
    }
}

internal sealed class SetPatientImmunizationsHandler : IRequestHandler<AddPatientImmunizationCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public SetPatientImmunizationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(AddPatientImmunizationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Immunization immunization = await session.Query<Domain.Immunization>().Where(x => x.Code == command.Code)
            .FirstOrDefaultAsync(cancellationToken);
        Guard.AgainstNotFound(immunization, new AppError("Immunization.NotFound", $"Immunization with 'code:' {command.Code} not found!"));
        Domain.PatientImmunization patientImmunization =
            Domain.PatientImmunization.Create(_user.PatientId!, immunization.Code, immunization.ShortDescription, command.Date);

        await session.StoreAsync(patientImmunization, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}