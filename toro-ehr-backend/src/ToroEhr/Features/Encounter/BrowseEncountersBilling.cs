using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record BrowseEncountersBillingQuery(PagedSearchParams PagedSearchParams, string Status)
    : AuthRequest<PaginatedList<EncountersBillingResponse>>;

public sealed record EncountersBillingResponse(
    string Id,
    string PatientName,
    DateTimeOffset EncounterDate,
    string? PhoneNumber,
    decimal AmountCharged,
    decimal AmountRefunded,
    string PaymentStatus,
    EncounterStatus BusinessStatus);

internal sealed class
    BrowseEncountersBillingAuth : IAuth<BrowseEncountersBillingQuery, PaginatedList<EncountersBillingResponse>>
{
    public BrowseEncountersBillingAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    BrowseEncountersBillingHandler : IRequestHandler<BrowseEncountersBillingQuery,
    PaginatedList<EncountersBillingResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public BrowseEncountersBillingHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<EncountersBillingResponse>> Handle(BrowseEncountersBillingQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Transactions_ByEncounter.Entry> dbQuery = session
            .Query<Transactions_ByEncounter.Entry, Transactions_ByEncounter>()
            .Where(x =>
                x.LocationId == _user.SelectedLocationId && (x.BusinessStatus == EncounterStatus.Completed.Name ||
                x.BusinessStatus == EncounterStatus.InProgress.Name ||
                x.BusinessStatus == EncounterStatus.Missed.Name ||
                x.BusinessStatus == EncounterStatus.CanceledLate.Name))
            .ProjectInto<Transactions_ByEncounter.Entry>();

        if (query.Status != "All")
        {
            dbQuery = dbQuery.Where(x => x.PaymentStatus == query.Status || x.BusinessStatus == query.Status);
        }

        List<Transactions_ByEncounter.Entry> entries = await dbQuery
            .OrderByDescending(x => x.EncounterDate)
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<EncountersBillingResponse>.Create(
            entries
                .Select(x => new EncountersBillingResponse(
                    x.EncounterId,
                    x.PatientName,
                    x.EncounterDate,
                    x.PatientPhone,
                    x.ChargedAmount,
                    x.RefundedAmount,
                    x.PaymentStatus,
                    EncounterStatus.FromName(x.BusinessStatus))).ToList(),
            stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize);
    }
}