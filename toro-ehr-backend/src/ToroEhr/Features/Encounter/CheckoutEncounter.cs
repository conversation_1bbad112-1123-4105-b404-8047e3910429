using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record CheckoutEncounterCommand(string EncounterId) : AuthRequest<Unit>;

internal sealed class CheckoutEncounterCommandAuth : IAuth<CheckoutEncounterCommand, Unit>
{
    public CheckoutEncounterCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class CheckoutEncounterCommandValidator : AbstractValidator<CheckoutEncounterCommand>
{
    public CheckoutEncounterCommandValidator()
    {
        RuleFor(x => x.EncounterId).NotEmpty();
    }
}

internal sealed class CheckoutEncounterCommandHandler : IRequestHandler<CheckoutEncounterCommand, Unit>
{
    private readonly IDocumentStore _store;

    public CheckoutEncounterCommandHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(CheckoutEncounterCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // query appointment first and include the encounter to save a db call
        Domain.Appointment appointment = await session
            .Query<Domain.Appointment>()
            .Include(x => x.EncounterId)
            .FirstOrDefaultAsync(x => x.EncounterId == command.EncounterId, cancellationToken);

        Guard.AgainstNotFound(appointment,
            new("Appointment.NotFound", $"Appointment with encounter id '{command.EncounterId}' not found"));

        Domain.Encounter encounter = await session
            .LoadAsync<Domain.Encounter>(command.EncounterId, cancellationToken);

        Guard.AgainstNotFound(encounter,
            new("Encounter.NotFound", $"Encounter with id '{command.EncounterId}' not found"));

        encounter.Checkout(new DateTimeOffset(command.Timestamp, TimeSpan.Zero));
        appointment.MarkAsCompleted();

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}
