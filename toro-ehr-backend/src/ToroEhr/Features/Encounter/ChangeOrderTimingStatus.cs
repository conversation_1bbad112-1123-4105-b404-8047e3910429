using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared.Interfaces;

namespace ToroEhr.Features.Encounter;

public record ChangeOrderTimingStatusCommand(List<string> OrderIds, string Status) : AuthRequest<Unit>;

internal class ChangeOrderTimingStatusAuth : IAuth<ChangeOrderTimingStatusCommand, Unit>
{
    public ChangeOrderTimingStatusAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class ChangeOrderTimingStatusValidator : AbstractValidator<ChangeOrderTimingStatusCommand>
{
    public ChangeOrderTimingStatusValidator()
    {
        RuleFor(x => x.OrderIds).NotEmpty();
        RuleFor(x => x.Status).NotEmpty();
    }
}

internal class ChangeOrderTimingStatusHandler : IRequestHandler<ChangeOrderTimingStatusCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ChangeOrderTimingStatusHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(ChangeOrderTimingStatusCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        ICollection<ITimingStatusOrder> orders = (await session.LoadAsync<ITimingStatusOrder>(command.OrderIds, cancellationToken)).Values;
        foreach (var orderMedication in orders)
        {
            orderMedication.UpdateTimingStatus(command.Status);
            if (command.Status == "Administered")
            {
                orderMedication.AddToAdministations(new ValueObjects.Administation(DateTimeOffset.UtcNow, _user.EmployeeId!, _user.FullName));
            }
        }

        await session.SaveChangesAsync();

        return Unit.Value;
    }
}