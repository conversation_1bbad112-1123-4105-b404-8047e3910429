using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record EditOrdeImmunizationCommand(string Id, string? BundleId, string ImmunizationId,
    string Note) : AuthRequest<Unit>;

internal class EditOrdeImmunizationAuth : IAuth<EditOrdeImmunizationCommand, Unit>
{
    public EditOrdeImmunizationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class EditOrdeImmunizationValidator : AbstractValidator<EditOrdeImmunizationCommand>
{
    public EditOrdeImmunizationValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.ImmunizationId).NotEmpty();
    }
}

internal class EditOrdeImmunizationHandler : IRequestHandler<EditOrdeImmunizationCommand, Unit>
{
    private readonly IDocumentStore _store;

    public EditOrdeImmunizationHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(EditOrdeImmunizationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Immunization immunization = await session.LoadAsync<Domain.Immunization>(command.ImmunizationId);
        Guard.AgainstNotFound(immunization, new("LoincCode.NotFound", $"LoincCode with 'id:' {command.ImmunizationId} not found!"));

        if (command.BundleId.IsNotNullOrWhiteSpace())
        {
            OrderBundle orderBundle = await session.LoadAsync<OrderBundle>(command.BundleId);
            Guard.AgainstNotFound(orderBundle, new("Order.NotFound", $"Order Bundle with 'id:' {command.Id} not found!"));

            var bundleOrderImmunization = orderBundle.Orders.FirstOrDefault(x => x.Id == command.Id);
            if (bundleOrderImmunization is OrderImmunization oi)
            {
                oi.Update(command.ImmunizationId, command.Note, immunization.DisplayName ?? string.Empty);
            }
        }
        else
        {
            OrderImmunization orderImmunization = await session.LoadAsync<OrderImmunization>(command.Id);
            Guard.AgainstNotFound(orderImmunization, new("Order.NotFound", $"Order with 'id:' {command.Id} not found!"));

            orderImmunization.Update(command.ImmunizationId, command.Note, immunization.DisplayName ?? string.Empty);
        }

        await session.SaveChangesAsync();

        return Unit.Value;
    }
}
