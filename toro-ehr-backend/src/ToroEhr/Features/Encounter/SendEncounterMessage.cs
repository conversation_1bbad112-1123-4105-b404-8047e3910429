using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Enums;
using ToroEhr.Domain;
using ToroEhr.Services;
using ToroEhr.Infrastructure;
using ToroEhr.Features.Shared;
using ToroEhr.Features.Encounter.Events;
using ToroEhr.ValueObjects;

namespace ToroEhr.Features.Encounter;

public sealed record SendEncounterMessageCommand(string MessageType, string EncounterId, string Subject, 
    string Message, List<IFormFile>? Attachments, bool SentByPatient)
    : AuthRequest<Unit>;

internal sealed class SendEncounterMessageAuth : IAuth<SendEncounterMessageCommand, Unit>
{
    public SendEncounterMessageAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

internal sealed class SendEncounterMessageValidator : AbstractValidator<SendEncounterMessageCommand>
{
    public SendEncounterMessageValidator()
    {
        RuleFor(x => x.Subject).NotEmpty();
        RuleFor(x => x.Message).NotEmpty();
        RuleFor(x => x.MessageType)
            .Must(s => MessageType.TryFromName(s, out _))
            .WithMessage(x => $"Message Type '{x}' not supported");
    }
}

internal sealed class SendEncounterMessageHandler : IRequestHandler<SendEncounterMessageCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly S3FileService _fileService;
    private readonly EmailService _emailService;
    private readonly UserRequestSession _user;

    public SendEncounterMessageHandler(IDocumentStore store, Authenticator authenticator, S3FileService fileService, EmailService emailService)
    {
        _store = store;
        _fileService = fileService;
        _emailService = emailService;
        _user = authenticator.User;

    }

    public async Task<Unit> Handle(SendEncounterMessageCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        CommunicationEntity message;
        BaseEventEntity? notification = null;

        var encounter = await session.LoadAsync<Domain.Encounter>(command.EncounterId, cancellationToken);

        var patient = await session.LoadAsync<Domain.Patient>(encounter.PatientId, cancellationToken);
        Sender sender = new Sender(command.SentByPatient, command.SentByPatient ? _user.PatientId! : _user.EmployeeId!, _user.FullName, _user.Email);

        switch (command.MessageType)
        {
            case nameof(MessageType.Sms):
                message = EncounterSms.Create(patient.Id, sender, command.EncounterId, command.Subject,
                    command.Message, DateTimeOffset.UtcNow);
                notification = new SendEncounterSms((EncounterSms)message, patient);
                break;
            case nameof(MessageType.Call):
                message = EncounterCallRecord.Create(patient.Id, sender, command.EncounterId, command.Subject,
                    command.Message, DateTimeOffset.UtcNow);
                break;
            default:
                var attachments = new List<string>();
                if (command.Attachments != null)
                {
                    foreach (var attachment in command.Attachments)
                    {
                        var filePath = $"patients/{patient.Id}/encounters/{command.EncounterId}/emails/{attachment.FileName}";
                        await using var stream = attachment.OpenReadStream();
                        await _fileService.UploadFile(stream, attachment.ContentType, Config.S3.AppFilesBucketName, filePath);
                        attachments.Add(filePath);
                    }
                }
                message = EncounterEmail.Create(patient.Id, sender, command.EncounterId, command.Subject,
                    command.Message, DateTimeOffset.UtcNow, attachments, null);
                if (!command.SentByPatient)
                {
                    notification = new SendEncounterEmail((EncounterEmail)message, patient);
                }
                break;
        }

        await session.StoreAsync(message, cancellationToken);
        if (notification != null)
        {
            await session.StoreAsync(notification, cancellationToken);
        }

        // create in-app notification for the recipient
        if (command.SentByPatient)
        {
            // patient sent message to practitioner - notify practitioner
            var locationEmployee = await session.Query<Domain.LocationEmployee>()
                .Where(x => x.EmployeeId == encounter.PractitionerId && x.LocationId == encounter.LocationId)
                .FirstOrDefaultAsync(cancellationToken);

            await NotificationService.CreateMessageReceivedNotification(
                session,
                encounter.PractitionerId,
                UserRole.Employee.Name,
                _user.FullName,
                command.MessageType,
                command.EncounterId,
                locationEmployee?.ReceivedNotificationPreferences,
                command.Timestamp);
        }
        else
        {
            // practitioner sent message to patient - notify patient
            await NotificationService.CreateMessageReceivedNotification(
                session,
                patient.Id,
                UserRole.Patient.Name,
                _user.FullName,
                command.MessageType,
                command.EncounterId,
                null, // patients don't have notification preferences for messages yet
                command.Timestamp);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}