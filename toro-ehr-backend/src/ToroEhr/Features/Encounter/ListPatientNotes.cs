using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record ListPatientNotesQuery(string PatientId) : AuthRequest<List<PatientNoteResponse>>;

internal sealed class ListPatientNotesAuth : IAuth<ListPatientNotesQuery, List<PatientNoteResponse>>
{
    public ListPatientNotesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    ListPatientNotesHandler : IRequestHandler<ListPatientNotesQuery, List<PatientNoteResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPatientNotesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<PatientNoteResponse>> Handle(ListPatientNotesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        List<Domain.Note> notes = await session
            .Query<Domain.Note>()
            .Include(x => x.PractitionerId)
            .Where(x => x.PatientId == query.PatientId && x.IsLatest)
            .OrderByDescending(x => x.SignedAt)
            .ToListAsync(token: cancellationToken);

        List<PatientNoteResponse> result = [];

        foreach (var note in notes)
        {
            var practitioner = await session.LoadAsync<Domain.Employee>(note.PractitionerId, cancellationToken);
            
            result.Add(new PatientNoteResponse(
                note.Id,
                note.NoteId,
                note.Version,
                note.Name,
                note.Classification,
                practitioner!.ShortName,
                note.SignedAt,
                note.Fields.Select(f => new NoteFieldResponse(f.Name, f.Value, true)).ToList(),
                note.CptCode,
                note.IcdCodes));
        }
        return result;
    }
}