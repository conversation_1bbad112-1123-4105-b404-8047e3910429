using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record MarkMessageAsSeenCommand(string MessageId)
    : AuthRequest<Unit>;

internal sealed class MarkMessageAsSeenCommandAuth : IAuth<UpdateScratchTextCommand, Unit>
{
    public MarkMessageAsSeenCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class MarkMessageAsSeenCommandValidator : AbstractValidator<MarkMessageAsSeenCommand>
{
    public MarkMessageAsSeenCommandValidator()
    {
        RuleFor(x => x.MessageId).NotEmpty();
    }
}

internal sealed class MarkMessageAsSeenHandler : IRequestHandler<MarkMessageAsSeenCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public MarkMessageAsSeenHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(MarkMessageAsSeenCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var message = await session.LoadAsync<Domain.CommunicationEntity>(command.MessageId, cancellationToken);
        message.MarkAsSeen();

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}