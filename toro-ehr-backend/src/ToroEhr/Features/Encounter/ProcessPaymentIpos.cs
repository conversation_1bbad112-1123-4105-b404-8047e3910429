﻿using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record ProcessPaymentIposCommand(string EncounterId, double Amount) : AuthRequest<ProcessPaymentResponse>;

internal sealed class ProcessPaymentIposCommandAuth : IAuth<ProcessPaymentIposCommand, ProcessPaymentResponse>
{
    public ProcessPaymentIposCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ProcessPaymentIposCommandValidator : AbstractValidator<ProcessPaymentIposCommand>
{
    public ProcessPaymentIposCommandValidator()
    {
        RuleFor(x => x.EncounterId).NotEmpty();
    }
}

internal sealed class ProcessPaymentIposHandler : IRequestHandler<ProcessPaymentIposCommand, ProcessPaymentResponse>
{
    private readonly IDocumentStore _store;
    private readonly IposService _iposService;

    public ProcessPaymentIposHandler(IDocumentStore store, Authenticator authenticator, IposService iposService)
    {
        _store = store;
        _iposService = iposService;
    }

    public async Task<ProcessPaymentResponse> Handle(ProcessPaymentIposCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Encounter encounter = await session
            .Include<Domain.Encounter>(x => x.LocationId)
            .LoadAsync<Domain.Encounter>(command.EncounterId, cancellationToken);

        Guard.AgainstNotFound(encounter, EncounterErrors.NotFoundById(command.EncounterId));

        Domain.Location location = await session.LoadAsync<Domain.Location>(encounter.LocationId, cancellationToken);

        if (location.IposPaysConfig == null)
        {
            return new ProcessPaymentResponse(false, "Payment is not enabled for this location");
        }

        string referenceId = Utils.GenerateRandomId();
        IposTransactionResponse iposTransactionResponse = await _iposService.InitiatePosTransactionAsync(
            new IposTransactionRequest(command.Amount, referenceId, location.IposPaysConfig.Tpn,
                location.IposPaysConfig.AuthKey));
        if (iposTransactionResponse.GeneralResponse.ResultCode == "0")
        {
            PaymentTransaction paymentTransaction = PaymentTransaction.CreateSuccessTransaction(
                encounter.PatientId, encounter.Id, TransactionType.Charge.Name, PaymentMethod.PosTerminal.Name,
                iposTransactionResponse.CardData.CardType, iposTransactionResponse.CardData.Last4,
                iposTransactionResponse.ReferenceId, iposTransactionResponse.Amounts.TotalAmount, command.Timestamp,
                TransactionStatus.Approved.Name, iposTransactionResponse.GeneralResponse.StatusCode,
                iposTransactionResponse.ExtendedDataByApplication?.Values.FirstOrDefault()?.TxnId,
                iposTransactionResponse.RRN);
            encounter.SetToPaid();
            await session.StoreAsync(paymentTransaction, cancellationToken);
            await session.StoreAsync(encounter, cancellationToken);

            PatientPaymentCard paymentCard = await session.Query<PatientPaymentCard>()
                .FirstOrDefaultAsync(x => x.IposToken == iposTransactionResponse.IPosToken, cancellationToken);

            if (paymentCard == null)
            {
                paymentCard = PatientPaymentCard.Create(encounter.PatientId, iposTransactionResponse.CardData.CardType,
                    iposTransactionResponse.CardData.Last4, iposTransactionResponse.CardData.ExpirationDate,
                    iposTransactionResponse.CardData.Name,
                    iposTransactionResponse.IPosToken);
                await session.StoreAsync(paymentCard, cancellationToken);
            }

            await session.SaveChangesAsync(cancellationToken);

            return new ProcessPaymentResponse(true, "Payment completed successfully.");
        }
        else
        {
            PaymentTransaction paymentTransaction = PaymentTransaction.CreateFailedTransaction(
                encounter.PatientId, encounter.Id, TransactionType.Charge.Name, PaymentMethod.PosTerminal.Name,
                iposTransactionResponse.CardData.CardType, iposTransactionResponse.CardData.Last4,
                iposTransactionResponse.ReferenceId, iposTransactionResponse.Amounts.TotalAmount, command.Timestamp,
                TransactionStatus.Declined.Name, iposTransactionResponse.GeneralResponse.StatusCode,
                iposTransactionResponse.GeneralResponse.DetailedMessage,
                iposTransactionResponse.ExtendedDataByApplication?.Values?.FirstOrDefault()?.TxnId,
                iposTransactionResponse.RRN);
            await session.StoreAsync(paymentTransaction, cancellationToken);

            await session.SaveChangesAsync(cancellationToken);

            return new ProcessPaymentResponse(false, iposTransactionResponse.GeneralResponse.DetailedMessage);
        }
    }
}