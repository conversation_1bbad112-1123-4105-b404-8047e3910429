using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Domain;

namespace ToroEhr.Features.Encounter;

public record MarkEncounterNoteCompletedCommand(string Id) : AuthRequest<Unit>;

internal class MarkEncounterNoteCompletedAuth : IAuth<MarkEncounterNoteCompletedCommand, Unit>
{
    public MarkEncounterNoteCompletedAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class MarkEncounterNoteCompletedValidator : AbstractValidator<MarkEncounterNoteCompletedCommand>
{
    public MarkEncounterNoteCompletedValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

internal class MarkEncounterNoteCompletedHandler : IRequestHandler<MarkEncounterNoteCompletedCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public MarkEncounterNoteCompletedHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(MarkEncounterNoteCompletedCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Note encounterNote = await session.LoadAsync<Note>(command.Id, cancellationToken);
        encounterNote.MarkCompleted();

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}