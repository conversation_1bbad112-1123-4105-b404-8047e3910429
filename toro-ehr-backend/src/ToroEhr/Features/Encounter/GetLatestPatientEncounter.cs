using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Features.Encounter.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record GetLatestPatientEncounterQuery(string PatientId) : AuthRequest<EncounterResponse?>;

internal sealed class GetLatestPatientEncounterAuth : IAuth<GetLatestPatientEncounterQuery, EncounterResponse?>
{
    public GetLatestPatientEncounterAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    GetLatestPatientEncounterHandler : IRequestHandler<GetLatestPatientEncounterQuery, EncounterResponse?>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetLatestPatientEncounterHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<EncounterResponse?> Handle(GetLatestPatientEncounterQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Encounter encounter = await session.Query<Domain.Encounter>()
            .Include(x => x.PatientId)
            .Where(x => x.PatientId == query.PatientId && x.LocationId == _user.SelectedLocationId)
            .OrderByDescending(x => x.StartAt)
            .FirstOrDefaultAsync();
        if (encounter == null)
        {
            return null;
        }

        Domain.Patient patient =
            await session.LoadAsync<Domain.Patient>(query.PatientId, cancellationToken);

        return ToEncounterResponse(encounter, patient,
            encounter?.StartAt);
    }

    private static EncounterResponse ToEncounterResponse(Domain.Encounter encounter, Domain.Patient patient,
        DateTimeOffset? previouesEncounterDate)
    {
        return new EncounterResponse(
            encounter.Id,
            encounter.ScratchText,
            patient.Id,
            patient.Email,
            patient.FullName,
            patient.Birthday,
            patient.BirthSex,
            patient.PhoneNumbers.FirstOrDefault(x => x.IsPrimary)?.Number,
            patient.PreferredContactMethod,
            previouesEncounterDate
        );
    }
}