using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Indexes;
using ToroEhr.Features.Encounter.Shared;
using Raven.Client.Documents.Queries;
using ToroEhr.Enums;

namespace ToroEhr.Features.Encounter;

public record SearchOrderEntryQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<SearchOrderEntryResponse>>;

internal class SearchOrderEntryAuth : IAuth<SearchOrderEntryQuery, PaginatedList<SearchOrderEntryResponse>>
{
    public SearchOrderEntryAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class SearchOrderEntryHandler : IRequestHandler<SearchOrderEntryQuery, PaginatedList<SearchOrderEntryResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public SearchOrderEntryHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<SearchOrderEntryResponse>> Handle(SearchOrderEntryQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<OrderEntrySearch.Entry> entries = session
            .Query<OrderEntrySearch.Entry, OrderEntrySearch>()
            .ProjectInto<OrderEntrySearch.Entry>();

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            entries = entries.Search(x => x.SearchParams, query.PagedSearchParams.SearchParam,
                options: SearchOptions.And, @operator: SearchOperator.And);
        }

        if (_user.OrganizationType == OrganizationType.NonPrescribingOrganization.Name)
        {
            entries = entries.Where(x => x.Type != "med");
        }

        var results = await entries
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<SearchOrderEntryResponse>.Create(
            results
                .Select(x => new SearchOrderEntryResponse(
                    x.Id,
                    x.Code,
                    x.DisplayName,
                    x.Type)).ToList(),
            stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize
        );
    }
}