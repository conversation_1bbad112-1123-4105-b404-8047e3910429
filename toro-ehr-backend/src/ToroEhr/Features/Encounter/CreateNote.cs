using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record CreateNoteCommand(
    string Name,
    string Classification,
    string DocumentType,
    string PatientId,
    string EncounterId,
    List<NoteFieldRequest> Fields,
    string CptCode,
    List<string> IcdCodes)
    : AuthRequest<Unit>;

internal sealed class CreateNoteCommandAuth : IAuth<CreateNoteCommand, Unit>
{
    public CreateNoteCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class CreateNoteCommandValidator : AbstractValidator<CreateNoteCommand>
{
    public CreateNoteCommandValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.EncounterId).NotEmpty();
    }
}

internal sealed class CreateNoteHandler : IRequestHandler<CreateNoteCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateNoteHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(CreateNoteCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Note note = Note.Create(
            command.Name,
            command.Classification,
            command.DocumentType,
            command.EncounterId,
            command.PatientId,
            _user.EmployeeId!,
            command.Fields.Select(x => new NoteField(x.Name, x.Value)).ToList(),
            command.CptCode,
            command.IcdCodes,
            command.Timestamp);

        await session.StoreAsync(note, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}