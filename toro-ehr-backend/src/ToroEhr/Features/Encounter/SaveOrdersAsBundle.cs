using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record SaveOrdersAsBundleCommand(List<string> OrderIds, string EncounterId, string Name, string Priority) : AuthRequest<Unit>;

internal class SaveOrdersAsBundleAuth : IAuth<SaveOrdersAsBundleCommand, Unit>
{
    public SaveOrdersAsBundleAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class SaveOrdersAsBundleValidator : AbstractValidator<SaveOrdersAsBundleCommand>
{
    public SaveOrdersAsBundleValidator()
    {
        RuleFor(x => x.OrderIds).NotEmpty();
        RuleFor(x => x.EncounterId).NotEmpty();
    }
}

internal class SaveOrdersAsBundleHandler : IRequestHandler<SaveOrdersAsBundleCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public SaveOrdersAsBundleHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(SaveOrdersAsBundleCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Encounter encounter = await session.LoadAsync<Domain.Encounter>(command.EncounterId, cancellationToken);
        ICollection<Order> orders = (await session.LoadAsync<Order>(command.OrderIds, cancellationToken)).Values;

        List<Order> bundleOrders = new List<Order>();

        foreach (var order in orders)
        {
            if (order is OrderBundle ob)
            {
                bundleOrders.AddRange(ob.Orders);
            }
            else
            {
                bundleOrders.Add(order);
            }
            session.Delete(order);
        }

        var newBundle = OrderBundle.Create(encounter.Id, encounter.PatientId, null, command.Name, command.Priority, bundleOrders, command.Timestamp, _user.EmployeeId!);
        await session.StoreAsync(newBundle);
        await session.SaveChangesAsync();

        return Unit.Value;
    }
}