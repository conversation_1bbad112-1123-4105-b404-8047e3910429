using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public record CreateOrderProcedureCommand(string EncounterId, string PatientId, string SnomedCodeId,
    string Note, bool Fasting, bool Repeat, string Priority) : AuthRequest<string>;

internal class CreateOrderProcedureAuth : IAuth<CreateOrderProcedureCommand, string>
{
    public CreateOrderProcedureAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class CreateOrderProcedureValidator : AbstractValidator<CreateOrderProcedureCommand>
{
    public CreateOrderProcedureValidator()
    {
        RuleFor(x => x.EncounterId).NotEmpty();
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.SnomedCodeId).NotEmpty();
        RuleFor(x => x.Priority).NotEmpty();
    }
}

internal class CreateOrderProcedureHandler : IRequestHandler<CreateOrderProcedureCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateOrderProcedureHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateOrderProcedureCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        string displayTerm = string.Empty;

        if (command.SnomedCodeId.StartsWith("Cpt"))
        {
            Domain.CptCode cptCode = await session.LoadAsync<Domain.CptCode>(command.SnomedCodeId);
            Guard.AgainstNotFound(cptCode, new("CptCode.NotFound", $"CptCode with 'id:' {command.SnomedCodeId} not found!"));
            displayTerm = cptCode.ClinicianDescriptor;
        }
        else
        {
            Domain.SnomedCode snomedCode = await session.LoadAsync<Domain.SnomedCode>(command.SnomedCodeId);
            Guard.AgainstNotFound(snomedCode, new("SnomedCode.NotFound", $"SnomedCode with 'id:' {command.SnomedCodeId} not found!"));
            displayTerm = snomedCode.Term;
        }


        OrderProcedure orderProcedure = OrderProcedure.Create(command.EncounterId, command.PatientId, command.SnomedCodeId,
            command.Note, command.Fasting, command.Repeat, displayTerm, command.Priority,
            DateTimeOffset.UtcNow, _user.EmployeeId!);

        await session.StoreAsync(orderProcedure, cancellationToken);
        await session.SaveChangesAsync();

        return orderProcedure.Id;
    }
}