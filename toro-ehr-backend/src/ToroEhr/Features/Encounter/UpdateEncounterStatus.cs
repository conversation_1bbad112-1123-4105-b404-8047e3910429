using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Exceptions;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.Encounter;

public sealed record UpdateEncounterStatusCommand(string EncounterId, string Status) : AuthRequest<string>;

internal class UpdateEncounterStatusAuth : IAuth<UpdateEncounterStatusCommand, string>
{
    public UpdateEncounterStatusAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal class UpdateEncounterStatusHandler : IRequestHandler<UpdateEncounterStatusCommand, string>
{
    private readonly IDocumentStore _store;

    public UpdateEncounterStatusHandler(IDocumentStore store, EmailService emailService)
    {
        _store = store;
    }

    public async Task<string> Handle(UpdateEncounterStatusCommand command,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Appointment appointment = await session
            .Query<Domain.Appointment>()
            .Include(x => x.EncounterId)
            .FirstOrDefaultAsync(x => x.EncounterId == command.EncounterId, token: cancellationToken);

        Guard.AgainstNotFound(appointment,
            new("Appointment.NotFound", $"Appointment with 'id:' {command.EncounterId} not found!"));

        Domain.Encounter encounter =
            await session.LoadAsync<Domain.Encounter>(appointment.EncounterId, cancellationToken);

        EncounterStatus status = EncounterStatus.FromName(command.Status);

        // check if trying to set status to InProgress and there's already an InProgress encounter for same patient/practitioner
        if (status == EncounterStatus.InProgress)
        {
            var existingInProgressEncounter = await session
                .Query<Domain.Encounter>()
                .Where(e => e.PatientId == encounter.PatientId
                           && e.PractitionerId == encounter.PractitionerId
                           && e.Status == EncounterStatus.InProgress.Name
                           && e.Id != encounter.Id)
                .FirstOrDefaultAsync(cancellationToken);

            if (existingInProgressEncounter != null)
            {
                throw new ValidationException([EncounterErrors.MultipleInProgressEncounters(encounter.PatientId, encounter.PractitionerId)]);
            }
        }

        encounter.UpdateStatus(status, command.Timestamp);

        if (status == EncounterStatus.CheckedIn)
        {
            appointment.CheckIn();
        }
        
        if (status == EncounterStatus.InProgress)
        {
            appointment.MarkAsInProgress();
        }

        await session.SaveChangesAsync(cancellationToken);
        return appointment.Id;
    }
}