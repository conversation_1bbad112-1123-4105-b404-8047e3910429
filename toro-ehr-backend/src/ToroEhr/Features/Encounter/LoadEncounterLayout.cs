using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.ValueObjects;

namespace ToroEhr.Features.Encounter;

public sealed record LoadEncounterLayoutQuery() : AuthRequest<List<EncounterLayoutResponse>>;

public record EncounterLayoutResponse(string Id, string Name, List<EncounterBox> EncounterBoxes, decimal ContainerHeight, decimal ContainerWidth);

internal sealed class LoadEncounterLayoutAuth : IAuth<LoadEncounterLayoutQuery, List<EncounterLayoutResponse>>
{
    public LoadEncounterLayoutAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    LoadEncounterLayoutHandler : IRequestHandler<LoadEncounterLayoutQuery, List<EncounterLayoutResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public LoadEncounterLayoutHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<EncounterLayoutResponse>> Handle(LoadEncounterLayoutQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        List<Domain.Layout> layouts = await session
            .Query<Domain.Layout>()
            .Where(x => x.UserId == _user.UserId)
            .ToListAsync(token: cancellationToken);

        return layouts.Select(x => new EncounterLayoutResponse(x.Id, x.Name, x.EncounterBoxes, x.ContainerHeight, x.ContainerWidth)).ToList();
    }
}