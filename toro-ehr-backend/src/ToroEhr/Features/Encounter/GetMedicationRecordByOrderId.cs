using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.ValueObjects;
using ToroEhr.Domain;

namespace ToroEhr.Features.Encounter;

public record GetMedicationRecordByOrderIdQuery(string EncaunterId, string OrderId, string? BundleId)
    : AuthRequest<MedicationRecordResponse>;

public record MedicationRecordResponse(
    string Id, 
    string Medication,
    string? Dose,
    string? Route,
    string MedicationId, 
    string Frequency,
    string CustomFrequency,
    string Duration, 
    bool Prn, 
    string? PrnReason, 
    DateTimeOffset StartTime, 
    string Instructions,
    string Priority, 
    string Status, 
    string? TimingStatus,
    string? Requester,
    string? Note,
    List<Administation> Administations);

internal class GetMedicationRecordByOrderIdAuth : IAuth<GetMedicationRecordByOrderIdQuery, MedicationRecordResponse>
{
    public GetMedicationRecordByOrderIdAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class
    GetMedicationRecordByOrderIdHandler : IRequestHandler<GetMedicationRecordByOrderIdQuery, MedicationRecordResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetMedicationRecordByOrderIdHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<MedicationRecordResponse> Handle(GetMedicationRecordByOrderIdQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderMedication? orderMed = null;

        if (query.BundleId != null)
        {
            OrderBundle orderBundle = await session
            .Include<OrderBundle>(x => x.CreatedBy)
            .LoadAsync<OrderBundle>(query.BundleId, cancellationToken);

            var order = orderBundle.Orders.FirstOrDefault(x => x.Id == query.OrderId);
            if (order is not null && order is OrderMedication om)
            {
                orderMed = om;
            }
        }
        else
        {
            orderMed = await session
            .Include<OrderMedication>(x => x.MedicationId)
            .Include<OrderMedication>(x => x.CreatedBy)
            .LoadAsync<OrderMedication>(query.OrderId, cancellationToken);
        }
        Guard.AgainstNotFound(orderMed, new("Order.NotFound", $"Order with 'id:' {query.OrderId} not found!"));

        Domain.Medication medication = await session
            .LoadAsync<Domain.Medication>(orderMed.MedicationId, cancellationToken);

        Domain.Employee practitioner = await session
            .LoadAsync<Domain.Employee>(orderMed.CreatedBy, cancellationToken);

        List<Domain.MedicationAttribute> medAttr = await session.Query<Domain.MedicationAttribute>()
            .Where(x => x.RxNormConceptId == medication.RxCui && (x.AttributeName == "DST" || x.AttributeName == "DRT"))
            .ToListAsync();


        return new MedicationRecordResponse(
            orderMed.Id, 
            orderMed.Name,
            medAttr.FirstOrDefault(x => x.AttributeName == "DST")?.AttributeValue,
            medAttr.FirstOrDefault(x => x.AttributeName == "DRT")?.AttributeValue,
            orderMed.MedicationId, 
            orderMed.Frequency,
            orderMed.CustomFrequency ?? string.Empty,
            orderMed.Duration, 
            orderMed.Prn,
            orderMed.PrnReason, 
            orderMed.StartTime, 
            orderMed.Instructions, 
            orderMed.Priority, 
            orderMed.Status, 
            orderMed.TimingStatus, 
            practitioner?.FullName,
            orderMed.Note,
            orderMed.Administations);
    }
}