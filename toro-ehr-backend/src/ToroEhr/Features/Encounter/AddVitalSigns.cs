using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record AddVitalSignsCommand(string PatientId, string EncounterId, string Status,
    List<VitalSignRequest> Vitals)
    : AuthRequest<Unit>;

public sealed record VitalSignRequest(string? Id, string Value, string Type);

internal sealed class AddVitalSignsCommandAuth : IAuth<AddVitalSignsCommand, Unit>
{
    public AddVitalSignsCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

internal sealed class AddVitalSignsCommandValidator : AbstractValidator<AddVitalSignsCommand>
{
    public AddVitalSignsCommandValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.EncounterId).NotEmpty();
    }
}

internal sealed class AddVitalSignsHandler : IRequestHandler<AddVitalSignsCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;


    public AddVitalSignsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(AddVitalSignsCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        
        Domain.Encounter encounter = await session.LoadAsync<Domain.Encounter>(command.EncounterId, cancellationToken);
        Domain.Patient patient = await session.LoadAsync<Domain.Patient>(command.PatientId, cancellationToken);

        Guard.AgainstNotFound(encounter, EncounterErrors.NotFoundById(patient.Id));
        Guard.AgainstNotFound(patient, PatientErrors.NotFoundById(patient.Id));

        var vitalSigns = await session.LoadAsync<VitalSign>(command.Vitals.Select(x => x.Id), cancellationToken);
        DateTimeOffset? recordedDate = command.Status == "final" ? command.Timestamp : null;
        string? recordedBy = command.Status == "final" ? _user.EmployeeId : null;

        foreach (var measurement in command.Vitals)
        {
            if (measurement.Id.IsNotNullOrWhiteSpace())
            {
                if (measurement.Value.IsNullOrWhiteSpace()) 
                {
                    session.Delete(measurement.Id);
                } else
                {
                    var vitalSign = vitalSigns.Values.First(x => x.Id == measurement.Id);
                    vitalSign.Update(measurement.Value, command.Status, recordedDate ?? vitalSign.RecordedDate,
                        recordedBy ?? vitalSign.RecordedBy, command.Timestamp, _user.EmployeeId!);
                }
            }
            else
            {

                var vitalSign = VitalSign.Create(
                    command.PatientId,
                    command.EncounterId,
                    measurement.Type,
                    measurement.Value,
                    command.Status,
                    recordedDate,
                    recordedBy,
                    command.Timestamp,
                    _user.EmployeeId!
                    );
                await session.StoreAsync(vitalSign, cancellationToken);
            }
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}