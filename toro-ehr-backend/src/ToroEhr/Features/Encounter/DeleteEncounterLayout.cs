using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using DocumentFormat.OpenXml.Spreadsheet;

namespace ToroEhr.Features.Encounter;

public record DeleteEncounterLayoutCommand(string LayoutId) : AuthRequest<Unit>;

internal class DeleteEncounterLayoutAuth : IAuth<DeleteEncounterLayoutCommand, Unit>
{
    public DeleteEncounterLayoutAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class DeleteEncounterLayoutValidator : AbstractValidator<DeleteEncounterLayoutCommand>
{
    public DeleteEncounterLayoutValidator()
    {
        RuleFor(x => x.LayoutId).NotEmpty();
    }
}

internal class DeleteEncounterLayoutHandler : IRequestHandler<DeleteEncounterLayoutCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public DeleteEncounterLayoutHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(DeleteEncounterLayoutCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Layout layout = await session.LoadAsync<Layout>(command.LayoutId, cancellationToken);
        if (layout.UserId != _user.UserId)
        {
            throw new ValidationException($"Layout '{command.LayoutId}' not belong to logged user.");
        }
        session.Delete(layout);

        await session.SaveChangesAsync();

        return Unit.Value;
    }
}