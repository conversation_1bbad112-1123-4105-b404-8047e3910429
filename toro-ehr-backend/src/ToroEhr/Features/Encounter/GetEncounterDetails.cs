using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Indexes;
using System.Threading;
using Raven.Client.Documents.Linq;

namespace ToroEhr.Features.Encounter;

public record GetEncounterDetailsQuery(string EncounterId) : AuthRequest<EncounterDetailsResponse>;

internal class GetEncounterDetailsAuth : IAuth<GetEncounterDetailsQuery, EncounterDetailsResponse>
{
    public GetEncounterDetailsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

public record EncounterDetailsResponse(
    string Id,
    DateTime EncounterDate,
    string EncounterLocationName,
    string PatientId,
    string PatientMrn,
    string PatientName,
    DateTime PatientDateOfBirth,
    string? PatientAddress,
    string PatientPhoneNumber,
    string PatientSex,
    string PractitionerName,
    List<PatientNoteResponse> Notes,
    List<VitalSignResponse> VitalSigns,
    List<EncounterQuestionnaireResponse> Questionnaires,
    List<EncounterMedicationResponse> Medications);

public record EncounterMedicationResponse(string Name, string Frequency, string Duration, string Instructions);

internal class GetEncounterDetailsHandler : IRequestHandler<GetEncounterDetailsQuery, EncounterDetailsResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetEncounterDetailsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<EncounterDetailsResponse> Handle(GetEncounterDetailsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var encounterEntry = await session
            .Query<Encounters_Comprehensive.Entry, Encounters_Comprehensive>()
            .Include(x => x.NoteIds)
            .Include(x => x.VitalSignIds)
            .Include(x => x.MedicationIds)
            .Include(x => x.QuestionnaireResponseIds)
            .Where(x => x.EncounterId == query.EncounterId)
            .ProjectInto<Encounters_Comprehensive.Entry>()
            .FirstOrDefaultAsync(cancellationToken);

        Guard.AgainstNotFound(encounterEntry, EncounterErrors.NotFoundById(query.EncounterId));

        var notes = encounterEntry.NoteIds.Any()
            ? await session.LoadAsync<Domain.Note>(encounterEntry.NoteIds, cancellationToken)
            : new Dictionary<string, Domain.Note>();

        var vitalSigns = encounterEntry.VitalSignIds.Any()
            ? await session
                .Include<Domain.VitalSign>(x => x.RecordedBy)
                .Include<Domain.VitalSign>(x => x.CreatedBy)
                .Include<Domain.VitalSign>(x => x.ModifiedBy)
                .LoadAsync<Domain.VitalSign>(encounterEntry.VitalSignIds, cancellationToken)
            : new Dictionary<string, Domain.VitalSign>();

        var questionnaireResponses = encounterEntry.QuestionnaireResponseIds.Any()
            ? await session.LoadAsync<Domain.QuestionnaireResponse>(encounterEntry.QuestionnaireResponseIds, cancellationToken)
            : new Dictionary<string, Domain.QuestionnaireResponse>();

        var medications = encounterEntry.MedicationIds.Any()
            ? await session.LoadAsync<Domain.OrderMedication>(encounterEntry.MedicationIds, cancellationToken)
            : new Dictionary<string, Domain.OrderMedication>();

        // process all data into response models
        var noteResponses = ProcessNotes(notes.Values.ToList(), encounterEntry.PractitionerShortName);
        var vitalSignResponses = await ProcessVitalSigns(vitalSigns.Values.ToList(), session, cancellationToken);
        var questionnaireResponseList = await ProcessQuestionnaires(session, questionnaireResponses.Values.ToList(), cancellationToken);
        var medicationResponses = ProcessMedications(medications.Values.ToList());

        return new EncounterDetailsResponse(
            encounterEntry.EncounterId,
            encounterEntry.StartAt.DateTime,
            encounterEntry.LocationName,
            encounterEntry.PatientId,
            encounterEntry.PatientMrn,
            encounterEntry.PatientFullName,
            encounterEntry.PatientBirthday.Date,
            encounterEntry.PatientAddress,
            encounterEntry.PatientPrimaryPhone ?? "",
            encounterEntry.PatientBirthSex ?? "",
            encounterEntry.PractitionerFullName,
            noteResponses,
            vitalSignResponses,
            questionnaireResponseList,
            medicationResponses);
    }

    private async Task<List<Domain.Note>> LoadNotes(IAsyncDocumentSession session, string encounterId, CancellationToken cancellationToken)
    {
        return await session
            .Query<Domain.Note>()
            .Where(x => x.EncounterId == encounterId && x.IsLatest)
            .OrderByDescending(x => x.SignedAt)
            .ToListAsync(token: cancellationToken);
    }

    private async Task<List<Domain.VitalSign>> LoadVitalSigns(IAsyncDocumentSession session, string encounterId, CancellationToken cancellationToken)
    {
        return await session
            .Query<Domain.VitalSign>()
            .Where(x => x.EncounterId == encounterId)
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    private async Task<List<Domain.QuestionnaireResponse>> LoadQuestionnaireResponses(IAsyncDocumentSession session, string encounterId, string patientId, CancellationToken cancellationToken)
    {
        return await session
            .Query<Domain.QuestionnaireResponse>()
            .Include(x => x.QuestionnaireId)
            .Where(x => x.EncounterId == encounterId ||
                        (x.EncounterId == null && x.PatientId == patientId))
            .ToListAsync(token: cancellationToken);
    }

    private async Task<List<Domain.OrderMedication>> LoadMedications(IAsyncDocumentSession session, string encounterId, CancellationToken cancellationToken)
    {
        // Load medication orders using index
        List<Orders_ByEncounter.Entry> medicationOrderEntries = await session
            .Query<Orders_ByEncounter.Entry, Orders_ByEncounter>()
            .Where(x => x.EncounterId == encounterId && x.OrderType == nameof(Domain.OrderMedication))
            .ToListAsync(cancellationToken);

        var medicationOrderIds = medicationOrderEntries.Select(x => x.OrderId).ToList();
        var medicationOrdersDict = await session.LoadAsync<Domain.OrderMedication>(medicationOrderIds, cancellationToken);

        return medicationOrdersDict.Values.ToList();
    }

    private List<PatientNoteResponse> ProcessNotes(List<Domain.Note> notes, string practitionerShortName)
    {
        List<PatientNoteResponse> noteResponses = [];
        // sort by SignedAt descending (most recent first)
        foreach (var note in notes.OrderByDescending(x => x.SignedAt))
        {
            noteResponses.Add(new PatientNoteResponse(
                note.Id,
                note.NoteId,
                note.Version,
                note.Name,
                note.Classification,
                practitionerShortName,
                note.SignedAt,
                note.Fields.Select(f => new NoteFieldResponse(f.Name, f.Value, true)).ToList(),
                note.CptCode,
                note.IcdCodes));
        }
        return noteResponses;
    }

    private async Task<List<VitalSignResponse>> ProcessVitalSigns(List<Domain.VitalSign> vitalSigns, IAsyncDocumentSession session, CancellationToken cancellationToken)
    {
        var vitalSingsResponse = new List<VitalSignResponse>();

        foreach (var vs in vitalSigns.OrderByDescending(x => x.RecordedDate))
        {
            var recordedBy = await session.LoadAsync<Domain.Employee>(vs.RecordedBy, cancellationToken);
            var createdBy = await session.LoadAsync<Domain.Employee>(vs.CreatedBy, cancellationToken);
            var modifiedBy = await session.LoadAsync<Domain.Employee>(vs.ModifiedBy, cancellationToken);

            vitalSingsResponse.Add(new VitalSignResponse(
            vs.Id,
            vs.RecordedDate,
            recordedBy?.FullName,
            vs.Type,
            vs.Value,
            vs.Status,
            vs.CreatedAt,
            vs.ModifiedAt,
            createdBy.FullName,
            modifiedBy?.FullName));
        }
        return vitalSingsResponse;
    }

    private async Task<List<EncounterQuestionnaireResponse>> ProcessQuestionnaires(IAsyncDocumentSession session, List<Domain.QuestionnaireResponse> questionnaireResponses, CancellationToken cancellationToken)
    {
        var questionnaireIds = questionnaireResponses
            .Select(x => x.QuestionnaireId)
            .Distinct()
            .ToList();

        var questionnaires = await session.Query<Domain.Questionnaire>()
            .Where(x => x.Id.In(questionnaireIds) || x.QuestionnaireId.In(questionnaireIds))
            .ToListAsync(cancellationToken);

        return questionnaireResponses
            .Select(qr =>
            {
                var questionnaire = questionnaires.First(x => x.Id == qr.QuestionnaireId || x.QuestionnaireId == qr.QuestionnaireId);

                return new EncounterQuestionnaireResponse(
                    questionnaire.Title,
                    questionnaire.LocationId,
                    questionnaire.Questions.Select(q =>
                    {
                        var questionAnswers = qr.Answers
                            .FirstOrDefault(a => a.QuestionId == q.Id)?.Values ?? [];

                        return new QuestionResponse(q.Text, questionAnswers);
                    }).ToList());
            })
            .ToList();
    }

    private List<EncounterMedicationResponse> ProcessMedications(List<Domain.OrderMedication> medications)
    {
        List<EncounterMedicationResponse> medicationResponses = [];
        foreach (var medicationOrder in medications)
        {
            var frequency = medicationOrder.Frequency.ToLower() == "custom"
                ? medicationOrder.CustomFrequency ?? medicationOrder.Frequency
                : medicationOrder.Frequency;

            medicationResponses.Add(new EncounterMedicationResponse(
                medicationOrder.Name,
                frequency,
                medicationOrder.Duration,
                medicationOrder.Instructions));
        }
        return medicationResponses;
    }
}