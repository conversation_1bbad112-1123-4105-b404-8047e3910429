using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Enums;

namespace ToroEhr.Features.Encounter;

public record CreateOrderImmunizationCommand(string EncounterId, string PatientId, string ImmunizationId,  string Note) 
    : AuthRequest<string>;

internal class CreateOrderImmunizationAuth : IAuth<CreateOrderImmunizationCommand, string>
{
    public CreateOrderImmunizationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class CreateOrderImmunizationValidator : AbstractValidator<CreateOrderImmunizationCommand>
{
    public CreateOrderImmunizationValidator()
    {
        RuleFor(x => x.EncounterId).NotEmpty();
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.ImmunizationId).NotEmpty();
    }
}

internal class CreateOrderImmunizationHandler : IRequestHandler<CreateOrderImmunizationCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateOrderImmunizationHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateOrderImmunizationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Immunization immunization = await session.LoadAsync<Domain.Immunization>(command.ImmunizationId);
        Guard.AgainstNotFound(immunization, new("Immunization.NotFound", $"Immunization with 'id:' {command.ImmunizationId} not found!"));


        OrderImmunization orderImmunization = OrderImmunization.Create(command.EncounterId, command.PatientId, command.ImmunizationId,
            command.Note, immunization.DisplayName, OrderPriorityType.Routine.Name, DateTimeOffset.UtcNow, _user.EmployeeId!);

        await session.StoreAsync(orderImmunization, cancellationToken);
        await session.SaveChangesAsync();

        return orderImmunization.Id;
    }
}
