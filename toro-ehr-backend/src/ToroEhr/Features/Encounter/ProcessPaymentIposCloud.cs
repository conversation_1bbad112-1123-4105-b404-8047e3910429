using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record ProcessPaymentIposCloudCommand(string EncounterId, decimal Amount, string CardId)
    : AuthRequest<ProcessPaymentResponse>;

internal sealed class ProcessPaymentIposCloudCommandAuth : IAuth<ProcessPaymentIposCloudCommand, ProcessPaymentResponse>
{
    public ProcessPaymentIposCloudCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ProcessPaymentIposCloudCommandValidator : AbstractValidator<ProcessPaymentIposCloudCommand>
{
    public ProcessPaymentIposCloudCommandValidator()
    {
        RuleFor(x => x.EncounterId).NotEmpty();
        RuleFor(x => x.CardId).NotEmpty();
    }
}

internal sealed class
    ProcessPaymentIposCloudHandler : IRequestHandler<ProcessPaymentIposCloudCommand, ProcessPaymentResponse>
{
    private readonly IDocumentStore _store;
    private readonly IposCloudService _iposService;

    public ProcessPaymentIposCloudHandler(IDocumentStore store, Authenticator authenticator,
        IposCloudService iposService)
    {
        _store = store;
        _iposService = iposService;
    }

    public async Task<ProcessPaymentResponse> Handle(ProcessPaymentIposCloudCommand command,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Encounter encounter = await session
            .Include<Domain.Encounter>(x => x.LocationId)
            .LoadAsync<Domain.Encounter>(command.EncounterId, cancellationToken);

        Guard.AgainstNotFound(encounter, EncounterErrors.NotFoundById(command.EncounterId));

        Domain.Location location = await session.LoadAsync<Domain.Location>(encounter.LocationId, cancellationToken);

        if (location.IposPaysConfig == null)
        {
            return new ProcessPaymentResponse(false, "Payment is not enabled for this location");
        }

        PatientPaymentCard paymentCard = await session.LoadAsync<PatientPaymentCard>(command.CardId, cancellationToken);

        string referenceId = Utils.GenerateRandomId();
        IposCloudTransactionRequest iposCloudTransactionRequest = new IposCloudTransactionRequest(
            new MerchantAuthentication(location.IposPaysConfig.CloudTpn, referenceId),
            new TransactionRequest(1, (int)(command.Amount * 100), paymentCard.IposToken));


        IposCloudTransactionResponse iposTransactionResponse =
            await _iposService.InitiatePosCloudTransactionAsync(iposCloudTransactionRequest,
                location.IposPaysConfig.CloudAuthKey);

        if (iposTransactionResponse.Iposhpresponse != null)
        {
            if (iposTransactionResponse.Iposhpresponse.ResponseCode == "200")
            {
                decimal amount = iposTransactionResponse.Iposhpresponse.TotalAmount / 100;

                PaymentTransaction paymentTransaction = PaymentTransaction.CreateSuccessTransaction(
                    encounter.PatientId, encounter.Id, TransactionType.Charge.Name, PaymentMethod.SavedCard.Name,
                    paymentCard.CardType, paymentCard.Last4,
                    iposTransactionResponse.Iposhpresponse.TransactionReferenceId, amount, command.Timestamp,
                    TransactionStatus.Approved.Name, iposTransactionResponse.Iposhpresponse.ResponseCode,
                    iposTransactionResponse.Iposhpresponse.TransactionId,
                    iposTransactionResponse.Iposhpresponse.Rrn);
                encounter.SetToPaid();
                await session.StoreAsync(paymentTransaction, cancellationToken);
                await session.StoreAsync(encounter, cancellationToken);
                await session.SaveChangesAsync(cancellationToken);
                return new ProcessPaymentResponse(true, "Payment completed successfully.");
            }
            else
            {
                decimal amount = iposTransactionResponse.Iposhpresponse.TotalAmount / 100;

                PaymentTransaction paymentTransaction = PaymentTransaction.CreateFailedTransaction(
                    encounter.PatientId, encounter.Id, TransactionType.Charge.Name, PaymentMethod.SavedCard.Name,
                    paymentCard.CardType, paymentCard.Last4,
                    iposTransactionResponse.Iposhpresponse.TransactionReferenceId, amount, command.Timestamp,
                    TransactionStatus.Declined.Name, iposTransactionResponse.Iposhpresponse.ResponseCode,
                    iposTransactionResponse.Iposhpresponse.ResponseMessage,
                    iposTransactionResponse.Iposhpresponse.TransactionId,
                    iposTransactionResponse.Iposhpresponse.Rrn);
                await session.StoreAsync(paymentTransaction, cancellationToken);
            }

            await session.SaveChangesAsync(cancellationToken);
            return new ProcessPaymentResponse(false, iposTransactionResponse.Iposhpresponse.ResponseMessage);
        }

        return new ProcessPaymentResponse(false, "Payment failed.");
    }
}