using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Services;
using ToroEhr.ValueObjects;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record SaveEncounterLayoutCommand(string Name, List<EncounterBox> EncounterBoxes, decimal ContainerHeight, decimal ContainerWidth)
    : IRequest<Unit>;

internal sealed class SaveEncounterLayoutValidator : AbstractValidator<SaveEncounterLayoutCommand>
{
    public SaveEncounterLayoutValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.EncounterBoxes).NotEmpty();
        RuleFor(x => x.ContainerHeight).NotEmpty();
        RuleFor(x => x.ContainerWidth).NotEmpty();
    }
}

internal sealed class SaveEncounterLayoutHandler : IRequestHandler<SaveEncounterLayoutCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public SaveEncounterLayoutHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(SaveEncounterLayoutCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var layout = Layout.Create(_user.UserId, command.Name, command.EncounterBoxes, command.ContainerHeight, command.ContainerWidth);

        await session.StoreAsync(layout, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);
        return Unit.Value;
    }
}