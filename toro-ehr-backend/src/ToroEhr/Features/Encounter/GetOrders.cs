using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Domain;
using System.Text.Json.Serialization;
using Raven.Client.Documents.Linq;
using ToroEhr.Indexes;
using Newtonsoft.Json;
using System.Reflection.Emit;

namespace ToroEhr.Features.Encounter;

public record GetOrdersQuery(string EncounterId) : AuthRequest<List<OrderResponse>>;

internal class GetOrdersAuth : IAuth<GetOrdersQuery, List<OrderResponse>>
{
    public GetOrdersAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "orderType")]
[JsonDerivedType(typeof(MedicationOrderResponse), "Med")]
[JsonDerivedType(typeof(LabOrderResponse), "Lab")]
[JsonDerivedType(typeof(ProcedureOrderResponse), "Procedure")]
[JsonDerivedType(typeof(BundleOrderResponse), "Bundle")]
public abstract record OrderResponse(string Id,/* string Type, string PatientId, */string Name,
    string Priority, string Status, /*bool IsRequired,*/ SearchOrderEntryResponse OrderEntry, DateTimeOffset? CompletedAt, string OrderType
    );
public record MedicationOrderResponse(string Id, string MedicationId, string IngredientId, string Frequency, string? CustomFrequency, string Duration,
bool Prn, string? PrnReason,  DateTimeOffset StartTime, string Instructions, string Name,/* bool IsRequired, */string Priority, string Status, string? TimingStatus, DateTimeOffset? CompletedAt, SearchOrderEntryResponse OrderEntry) :
OrderResponse(Id, Name, Priority, Status,/* TimingStatus, IsRequired, */OrderEntry, CompletedAt, "Med");

public record LabOrderResponse(string Id, string LoincCodeId, string Specimen, string? Note, bool Fasting, bool Repeat,
    string Name,/* bool IsRequired, */string Priority, string Status, DateTimeOffset? CompletedAt, SearchOrderEntryResponse OrderEntry) :
    OrderResponse(Id, Name, Priority, Status,/* TimingStatus, IsRequired,*/ OrderEntry, CompletedAt, "Lab");

public record ProcedureOrderResponse(string Id, string SnomedCodeId, string? Note, bool Fasting, bool Repeat,
    string Name,/* bool IsRequired, */string Priority, string Status, DateTimeOffset? CompletedAt, SearchOrderEntryResponse OrderEntry) :
    OrderResponse(Id, Name, Priority, Status,/* TimingStatus, IsRequired,*/ OrderEntry, CompletedAt, "Procedure");

public record BundleOrderResponse(string Id, string BundleTemplateId, string Name, List<OrderResponse> Orders,/* bool IsRequired, */string Priority, string Status, DateTimeOffset? CompletedAt, SearchOrderEntryResponse OrderEntry) :
    OrderResponse(Id, Name, Priority, Status,/* TimingStatus, IsRequired,*/ OrderEntry, CompletedAt, "Bundle");


internal class GetOrdersHandler : IRequestHandler<GetOrdersQuery, List<OrderResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetOrdersHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<OrderResponse>> Handle(GetOrdersQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        List<Orders_ByEncounter.Entry> entries = await session
            .Query<Orders_ByEncounter.Entry, Orders_ByEncounter>()
            .ProjectInto<Orders_ByEncounter.Entry>()
            .Include(x => x.OrderId)
            .Include(x => x.IngredientId)
            .Include(x => x.LoincCodeId)
            .Include(x => x.SnomedCodeId)
            .Where(x => x.EncounterId == query.EncounterId)
            .ToListAsync(cancellationToken);

        var orders = new List<Domain.Order>();

        foreach (var entry in entries)
        {
            switch (entry.OrderType)
            {
                case nameof(OrderMedication):
                    orders.Add(await session.LoadAsync<OrderMedication>(entry.OrderId));
                    break;
                case nameof(OrderLab):
                    orders.Add(await session.LoadAsync<OrderLab>(entry.OrderId));
                    break;
                case nameof(OrderProcedure):
                    orders.Add(await session.LoadAsync<OrderProcedure>(entry.OrderId));
                    break;
                case nameof(OrderBundle):
                    orders.Add(await session.LoadAsync<OrderBundle>(entry.OrderId));
                    break;
            }
            
        }

        var result = await MapEntriesAsync(orders, session, cancellationToken);


        return result;
    }

    private async Task<List<OrderResponse>> MapEntriesAsync(IEnumerable<Domain.Order> orders, IAsyncDocumentSession session, CancellationToken ct)
    {
        var result = new List<OrderResponse>();

        foreach (var order in orders)
        {
            switch (order)
            {
                case OrderMedication orderMed:
                    Domain.Medication ingredient = await session.LoadAsync<Domain.Medication>(orderMed.IngredientId, ct);

                    result.Add(new MedicationOrderResponse(orderMed.Id, orderMed.MedicationId, orderMed.IngredientId, orderMed.Frequency, orderMed.CustomFrequency,
                        orderMed.Duration, orderMed.Prn, orderMed.PrnReason, orderMed.StartTime, orderMed.Instructions, orderMed.Name, orderMed.Priority,
                        orderMed.Status, orderMed.TimingStatus, orderMed.CompletedAt, new SearchOrderEntryResponse(ingredient.Id, ingredient.RxCui, ingredient.TermString, "Med")));
                    break;

                case OrderLab orderLab:
                    Domain.LoincCode loincCode = await session.LoadAsync<Domain.LoincCode>(orderLab.LoincCodeId, ct);

                    result.Add(new LabOrderResponse(orderLab.Id, orderLab.LoincCodeId, orderLab.Specimen, orderLab.Note, orderLab.Fasting, orderLab.Repeat,
                        orderLab.Name, orderLab.Priority, orderLab.Status, orderLab.CompletedAt,
                        new SearchOrderEntryResponse(loincCode.Id, loincCode.LoincNum ?? string.Empty, loincCode.DisplayName ?? string.Empty, "Lab")));
                    break;


                case OrderProcedure orderProcedure:

                    string entryId = string.Empty;
                    string entryCode = string.Empty;
                    string entryTerm = string.Empty;
                    if (orderProcedure.SnomedCodeId.StartsWith("Cpt"))
                    {
                        Domain.CptCode cptCode = await session.LoadAsync<Domain.CptCode>(orderProcedure.SnomedCodeId);
                        entryId = cptCode.Id;
                        entryCode = cptCode.Code;
                        entryTerm = $"CPT-{cptCode.ClinicianDescriptor}";
                    }
                    else
                    {
                        Domain.SnomedCode snomedCode = await session.LoadAsync<Domain.SnomedCode>(orderProcedure.SnomedCodeId);
                        entryId = snomedCode.Id;
                        entryCode = snomedCode.SnomedId;
                        entryTerm = $"SNOMED-{snomedCode.Term}";
                    }

                    result.Add(new ProcedureOrderResponse(
                        orderProcedure.Id, orderProcedure.SnomedCodeId, orderProcedure.Note, orderProcedure.Fasting, orderProcedure.Repeat, orderProcedure.Name,
                        orderProcedure.Priority, orderProcedure.Status, orderProcedure.CompletedAt,
                        new SearchOrderEntryResponse(entryId, entryCode, entryTerm, "Procedure")));
                    break;

                case OrderBundle orderBundle:
                    var nestedOrders = await MapEntriesAsync(orderBundle.Orders, session, ct); // 🔁 Recursive step
                    result.Add(new BundleOrderResponse(orderBundle.Id, orderBundle.BundleTemplateId, orderBundle.Name, nestedOrders, orderBundle.Priority, orderBundle.Status, orderBundle.CompletedAt,
                        new SearchOrderEntryResponse(orderBundle.Id, string.Empty, orderBundle.Name, "Bundle")));
                    break;

                default:
                    break;
            }
        }

        return result;
    }
}
