using FluentValidation;
using MediatR;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.TextFormatting;

public sealed record FormatTextCommand(string Text) : AuthRequest<FormatTextResponse>;

public sealed record FormatTextResponse(string FormattedText);

internal sealed class FormatTextValidator : AbstractValidator<FormatTextCommand>
{
    public FormatTextValidator()
    {
        RuleFor(x => x.Text)
            .NotEmpty()
            .WithMessage("Text is required")
            .MaximumLength(5000)
            .WithMessage("Text cannot exceed 5000 characters");
    }
}

internal sealed class FormatTextAuth : IAuth<FormatTextCommand, FormatTextResponse>
{
    public FormatTextAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class FormatTextHandler : IRequestHandler<FormatTextCommand, FormatTextResponse>
{
    private readonly OllamaService _ollamaService;

    public FormatTextHandler(OllamaService ollamaService)
    {
        _ollamaService = ollamaService;
    }

    public async Task<FormatTextResponse> Handle(FormatTextCommand command, CancellationToken cancellationToken)
    {
        var formattedText = await _ollamaService.FormatTextAsync(command.Text);
        return new FormatTextResponse(formattedText);
    }
}
