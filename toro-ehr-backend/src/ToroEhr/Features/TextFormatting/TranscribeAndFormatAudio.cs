using FluentValidation;
using MediatR;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.TextFormatting;

public sealed record TranscribeAndFormatAudioCommand(IFormFile AudioFile, string Model = "small") : AuthRequest<TranscribeAndFormatAudioResponse>;

public sealed record TranscribeAndFormatAudioResponse(string RawTranscription, string FormattedText);

internal sealed class TranscribeAndFormatAudioValidator : AbstractValidator<TranscribeAndFormatAudioCommand>
{
    public TranscribeAndFormatAudioValidator()
    {
        RuleFor(x => x.AudioFile)
            .NotNull()
            .WithMessage("Audio file is required");
            
        RuleFor(x => x.AudioFile.Length)
            .LessThanOrEqualTo(25 * 1024 * 1024) // 25MB limit for Whisper API
            .WithMessage("Audio file cannot exceed 25MB")
            .When(x => x.AudioFile != null);
            
        RuleFor(x => x.AudioFile.ContentType)
            .Must(contentType => IsValidAudioFormat(contentType))
            .WithMessage("Invalid audio format. Supported formats: mp3, mp4, mpeg, mpga, m4a, wav, webm")
            .When(x => x.AudioFile != null);

        RuleFor(x => x.Model)
            .Must(model => model == "small" || model == "turbo" || model == "distil")
            .WithMessage("Model must be either 'small', 'turbo', or 'distil'");
    }
    
    private static bool IsValidAudioFormat(string contentType)
    {
        var validFormats = new[]
        {
            "audio/mpeg", "audio/mp3", "audio/mp4", "audio/mpga", 
            "audio/m4a", "audio/wav", "audio/webm", "audio/x-wav"
        };
        return validFormats.Contains(contentType.ToLowerInvariant());
    }
}

internal sealed class TranscribeAndFormatAudioAuth : IAuth<TranscribeAndFormatAudioCommand, TranscribeAndFormatAudioResponse>
{
    public TranscribeAndFormatAudioAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class TranscribeAndFormatAudioHandler : IRequestHandler<TranscribeAndFormatAudioCommand, TranscribeAndFormatAudioResponse>
{
    private readonly WhisperService _whisperService;
    private readonly OllamaService _ollamaService;

    public TranscribeAndFormatAudioHandler(WhisperService whisperService, OllamaService ollamaService)
    {
        _whisperService = whisperService;
        _ollamaService = ollamaService;
    }

    public async Task<TranscribeAndFormatAudioResponse> Handle(TranscribeAndFormatAudioCommand command, CancellationToken cancellationToken)
    {
        // First transcribe the audio
        var rawTranscription = await _whisperService.TranscribeAudioAsync(command.AudioFile, command.Model);

        // Then format the transcribed text
        var formattedText = await _ollamaService.FormatTextAsync(rawTranscription);

        return new TranscribeAndFormatAudioResponse(rawTranscription, formattedText);
    }
}
