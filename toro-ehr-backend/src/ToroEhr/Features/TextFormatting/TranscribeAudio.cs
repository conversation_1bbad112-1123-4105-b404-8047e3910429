using FluentValidation;
using MediatR;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.TextFormatting;

public sealed record TranscribeAudioCommand(IFormFile AudioFile, string Model = "small") : AuthRequest<TranscribeAudioResponse>;

public sealed record TranscribeAudioResponse(string TranscribedText);

internal sealed class TranscribeAudioValidator : AbstractValidator<TranscribeAudioCommand>
{
    public TranscribeAudioValidator()
    {
        RuleFor(x => x.AudioFile)
            .NotNull()
            .WithMessage("Audio file is required");
            
        RuleFor(x => x.AudioFile.Length)
            .LessThanOrEqualTo(25 * 1024 * 1024) // 25MB limit for Whisper API
            .WithMessage("Audio file cannot exceed 25MB")
            .When(x => x.AudioFile != null);
            
        RuleFor(x => x.AudioFile.ContentType)
            .Must(contentType => IsValidAudioFormat(contentType))
            .WithMessage("Invalid audio format. Supported formats: mp3, mp4, mpeg, mpga, m4a, wav, webm")
            .When(x => x.AudioFile != null);

        RuleFor(x => x.Model)
            .Must(model => model == "small" || model == "turbo" || model == "distil")
            .WithMessage("Model must be either 'small', 'turbo', or 'distil'");
    }
    
    private static bool IsValidAudioFormat(string contentType)
    {
        var validFormats = new[]
        {
            "audio/mpeg", "audio/mp3", "audio/mp4", "audio/mpga", 
            "audio/m4a", "audio/wav", "audio/webm", "audio/x-wav"
        };
        return validFormats.Contains(contentType.ToLowerInvariant());
    }
}

internal sealed class TranscribeAudioAuth : IAuth<TranscribeAudioCommand, TranscribeAudioResponse>
{
    public TranscribeAudioAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class TranscribeAudioHandler : IRequestHandler<TranscribeAudioCommand, TranscribeAudioResponse>
{
    private readonly WhisperService _whisperService;

    public TranscribeAudioHandler(WhisperService whisperService)
    {
        _whisperService = whisperService;
    }

    public async Task<TranscribeAudioResponse> Handle(TranscribeAudioCommand command, CancellationToken cancellationToken)
    {
        var transcribedText = await _whisperService.TranscribeAudioAsync(command.AudioFile, command.Model);
        return new TranscribeAudioResponse(transcribedText);
    }
}
