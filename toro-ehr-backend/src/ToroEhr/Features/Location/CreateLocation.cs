using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared.Models;
using Raven.Client.Documents.Linq;
using ToroEhr.Domain;

namespace ToroEhr.Features.Location;

public sealed record CreateLocationCommand(
    string Name,
    string Classification,
    bool IsDefault,
    string PhoneNumber,
    string TaxIdentificationNumber,
    int MarkMissedTime,
    decimal MissedFeeInCents,
    int CheckInStartOffsetHours,
    string IposTpn,
    string IposAuthKey,
    string IposCloudTpn,
    string IposCloudAuthKey,
    bool SameDayAppointment,
    AddressRequest Address) : AuthRequest<string>;

internal sealed class CreateLocationCommandValidator : AbstractValidator<CreateLocationCommand>
{
    public CreateLocationCommandValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.PhoneNumber).NotEmpty();
        RuleFor(x => x.TaxIdentificationNumber).NotEmpty();
        RuleFor(x => x.Address).SetValidator(new AddressRequestValidator());
    }
}

internal sealed class CreateLocationAuth : IAuth<CreateLocationCommand, string>
{
    public CreateLocationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.OrganizationAdmin]);
    }
}

internal sealed class CreateLocationHandler : IRequestHandler<CreateLocationCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateLocationHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateLocationCommand request, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Address address = Domain.Address.Create(request.Address.Street, request.Address.City,
            request.Address.State, request.Address.ZipCode);

        Domain.Location location = Domain.Location.Create(_user.SelectedOrganizationId!, request.Name,
            request.Classification, request.IsDefault, request.PhoneNumber, request.TaxIdentificationNumber,
            request.MarkMissedTime, request.MissedFeeInCents, request.CheckInStartOffsetHours, request.SameDayAppointment, address,
            new IposPaysConfig(request.IposTpn, request.IposAuthKey, request.IposCloudTpn, request.IposCloudAuthKey));

        if (request.IsDefault)
        {
            var existingDefaultLocations = await session.Query<Domain.Location>()
                .Where(x => x.OrganizationId == _user.SelectedOrganizationId && x.IsDefault)
                .ToListAsync(token: cancellationToken);

            foreach (var existingDefaultLocation in existingDefaultLocations)
            {
                existingDefaultLocation.UpdateDefault(false);
            }
        }

        await session.StoreAsync(location, cancellationToken);

        Domain.LocationEmployee locationEmployee = Domain.LocationEmployee.Create(_user.SelectedOrganizationId!,
            location.Id, _user.EmployeeId!,
            [EmployeeRole.OrganizationAdmin.Name], string.Empty, string.Empty, string.Empty, request.Timestamp,
            false, null);
        await session.StoreAsync(locationEmployee, cancellationToken);

        await session.SaveChangesAsync(cancellationToken);

        return location.Id;
    }
}