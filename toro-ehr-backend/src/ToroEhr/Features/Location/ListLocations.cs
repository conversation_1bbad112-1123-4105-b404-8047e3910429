using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Features.Shared;

namespace ToroEhr.Features.Location;

public sealed record ListLocationsQuery(PagedSearchParams PagedSearchParams, bool IncludeDeactivated = false)
    : AuthRequest<PaginatedList<LocationResponse>>;

public sealed record LocationResponse(
    string Id,
    string Name,
    string? Classification,
    bool IsDefault,
    string PhoneNumber,
    string TaxIdentificationNumber,
    int MarkMissedTime,
    decimal MissedFee,
    int CheckInStartOffsetHours,
    bool SameDayAppointment,
    string? IposTpn,
    string? IposAuthKey,
    string? IposCloudTpn,
    string? IposCloudAuthKey,
    AddressResponse? Address);

internal sealed class ListLocationsAuth : IAuth<ListLocationsQuery, PaginatedList<LocationResponse>>
{
    public ListLocationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.OrganizationAdmin]);
    }
}

internal sealed class ListLocationsHandler : IRequestHandler<ListLocationsQuery, PaginatedList<LocationResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListLocationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<LocationResponse>> Handle(ListLocationsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        IRavenQueryable<Domain.Location> dbQuery = session.Query<Domain.Location>()
            .Where(x => x.OrganizationId == _user.SelectedOrganizationId);

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery.Search(x => x.Name, $"{query.PagedSearchParams.SearchParam}*");
        }

        if (!query.IncludeDeactivated)
        {
            dbQuery = dbQuery.Where(x => x.DeactivatedAt == null);
        }

        var locations = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<LocationResponse>.Create(
            locations.Select(x => new LocationResponse(x.Id, x.Name, x.Classification, x.IsDefault, x.PhoneNumber,
                x.TaxIdentificationNumber, x.MarkMissedTime, x.MissedFee, x.CheckInStartOffsetHours, x.SameDayAppointment,
                x.IposPaysConfig?.Tpn, x.IposPaysConfig?.AuthKey, x.IposPaysConfig?.CloudTpn, x.IposPaysConfig?.CloudAuthKey,
                x.Address != null
                    ? new AddressResponse(x.Address.Street, x.Address.City, x.Address.State, x.Address.ZipCode)
                    : null)).ToList(), stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}