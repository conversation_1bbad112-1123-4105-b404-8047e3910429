using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Features.Organization.Shared;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Shared;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Authentication.Events;
using ValidationException = ToroEhr.Infrastructure.Exceptions.ValidationException;

namespace ToroEhr.Features.Organization;

public record EditOrganizationCommand(string Id, string Name, string? ContactPersonId, 
    string DefaultLocationId, OrganizationAdminRequest? OrganizationAdmin, string Type) : AuthRequest<string>;

internal class EditOrganizationAuth : IAuth<EditOrganizationCommand, string>
{
    public EditOrganizationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public class EditOrganizationCommandValidator : AbstractValidator<EditOrganizationCommand>
{
    public EditOrganizationCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Type)
            .Must(s => OrganizationType.TryFromName(s, out _))
            .WithMessage(x => $"Organization Type '{x}' not supported");
    }
}

internal class EditOrganizationHandler : IRequestHandler<EditOrganizationCommand, string>
{
    private readonly IDocumentStore _store;

    public EditOrganizationHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<string> Handle(EditOrganizationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var organization = await session.LoadAsync<Domain.Organization>(command.Id, cancellationToken);
        Guard.AgainstNotFound(organization, OrganizationErrors.NotFoundById(command.Id));


        var defaultLocation = await session.Query<Domain.Location>().Where(x => x.OrganizationId == command.Id && x.IsDefault).FirstOrDefaultAsync(cancellationToken);
        if (defaultLocation.Id != command.DefaultLocationId)
        {
            defaultLocation.UpdateDefault(false);
            var newDefaultLocation = await session.LoadAsync<Domain.Location>(command.DefaultLocationId, cancellationToken);
            newDefaultLocation.UpdateDefault(true);
        }

        if (command.ContactPersonId.IsNullOrWhiteSpace())
        {
            User existingUser = await session.Query<User>()
                .Include(x => x.EmployeeId)
                .FirstOrDefaultAsync(x => x.PrimaryEmail == command.OrganizationAdmin!.Email, token: cancellationToken);

            if (existingUser != null)
            {
                var locationEmployees = await session.Query<Domain.LocationEmployee>()
                    .Where(x => x.OrganizationId == command.Id)
                    .ToListAsync(cancellationToken);

                Domain.Employee existingEmployee = await session.LoadAsync<Domain.Employee>(existingUser.EmployeeId, cancellationToken);
                if (locationEmployees.Any(x => x.EmployeeId == existingEmployee.Id))
                {
                    throw new ValidationException([OrganizationErrors.AlreadyExists(command.OrganizationAdmin!.Email)]);
                } 
                else
                {
                    LocationEmployee locationEmployee = LocationEmployee.Create(organization.Id, command.DefaultLocationId, existingEmployee.Id,
                        [EmployeeRole.OrganizationAdmin.Name], "#800080", string.Empty, Utils.GenerateRandomId(),
                        command.Timestamp, false, null);

                    await session.StoreAsync(locationEmployee, cancellationToken);

                    organization.Update(command.Name, existingEmployee.Id, command.Type);
                }
            }
            else
            {
                Domain.Employee organizationAdmin = Domain.Employee.Create(command.OrganizationAdmin!.Email, command.OrganizationAdmin.FirstName,
                    command.OrganizationAdmin.LastName, string.Empty, string.Empty, null);
                LocationEmployee locationEmployee = LocationEmployee.Create(organization.Id, command.DefaultLocationId, organizationAdmin.Id,
                    [EmployeeRole.OrganizationAdmin.Name], "#800080", string.Empty, Utils.GenerateRandomId(),
                    command.Timestamp, false, null);

                await session.StoreAsync(organizationAdmin, cancellationToken);
                await session.StoreAsync(locationEmployee, cancellationToken);

                User user =
                    User.Create(command.OrganizationAdmin!.Email, command.OrganizationAdmin.FirstName,
                        command.OrganizationAdmin.LastName, null, organizationAdmin.Id, [UserRole.Employee.Name]);
                await session.StoreAsync(user, cancellationToken);

                organization.Update(command.Name, organizationAdmin.Id, command.Type);

                // create a temporary user session for organization creation context
                var tempUser = new UserRequestSession(
                    "system", // UserId
                    "system-session", // SessionId
                    command.OrganizationAdmin.Email, // Email
                    command.OrganizationAdmin.FirstName, // FirstName
                    command.OrganizationAdmin.LastName, // LastName
                    UserRole.Employee.Name, // SelectedUserRole
                    organizationAdmin.Id, // EmployeeId
                    null, // PatientId
                    null, // SelectedOrganizationId - null for new org admin
                    null, // OrganizationName - null for new org admin
                    null, // OrganizationType
                    null, // SelectedLocationId
                    null, // LocationName
                    null,// SelectedLocationEmployeeRoles
                    null); //TimeZone

                await session.StoreAsync(
                    new SendInvitationEmail(command.OrganizationAdmin.FirstName, command.OrganizationAdmin.LastName,
                        command.OrganizationAdmin.Email, tempUser, locationEmployee.InvitationToken!,
                        [EmployeeRole.OrganizationAdmin.Name]), cancellationToken);
            }
        } 
        else
        {
            organization.Update(command.Name, command.ContactPersonId, command.Type);
        }

        await session.StoreAsync(organization, cancellationToken);

        await session.SaveChangesAsync(cancellationToken);

        return organization.Id;
    }
}