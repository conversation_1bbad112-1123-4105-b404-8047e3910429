using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Organization;

public sealed record ListOrganizationsQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<OrganizationResponse>>;

public sealed record OrganizationResponse(string Id, string Name, ContactPersonResponse? ContactPerson, string DefaultLocation, 
    string Type);
public sealed record ContactPersonResponse(string Id, string FullName, string Email);

internal sealed class ListOrganizationsAuth : IAuth<ListOrganizationsQuery, PaginatedList<OrganizationResponse>>
{
    public ListOrganizationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
};

internal sealed class
    ListOrganizationsHandler : IRequestHandler<ListOrganizationsQuery, PaginatedList<OrganizationResponse>>
{
    private readonly IDocumentStore _store;

    public ListOrganizationsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<PaginatedList<OrganizationResponse>> Handle(ListOrganizationsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<ContactPerson_ByOrganization.Entry> dbQuery = session
            .Query<ContactPerson_ByOrganization.Entry, ContactPerson_ByOrganization>()
            .ProjectInto<ContactPerson_ByOrganization.Entry>()
            .Include(x => x.OrganizationId)
            .Include(x => x.EmployeeId);

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery.Search(x => x.SearchParams, $"{query.PagedSearchParams.SearchParam}*");
        }

        var results = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        var organizations = new List<OrganizationResponse>();
        foreach (var result in results)
        {
            var organization = await session.LoadAsync<Domain.Organization>(result.OrganizationId);
            var employee = await session.LoadAsync<Domain.Employee>(result.EmployeeId);
            organizations.Add(new OrganizationResponse(organization.Id, organization.Name,
                organization.ContactEmployee != null ? 
                new ContactPersonResponse(employee.Id, employee.FullName, employee.Email) : null, result.DefaultLocationId,
                organization.Type));
        }

        return PaginatedList<OrganizationResponse>.Create(
            organizations, stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}