using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Authentication.Events;
using ToroEhr.Features.Organization.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Organization;

public record CreateOrganizationCommand(
    string OrganizationName,
    string LocationName,
    string Type,
    OrganizationAdminRequest OrganizationAdmin
) : AuthRequest<string>;



internal class CreateOrganizationAuth : IAuth<CreateOrganizationCommand, string>
{
    public CreateOrganizationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public class CreateOrganizationCommandValidator : AbstractValidator<CreateOrganizationCommand>
{
    public CreateOrganizationCommandValidator()
    {
        RuleFor(x => x.OrganizationName).NotEmpty();
        RuleFor(x => x.LocationName).NotEmpty();
        RuleFor(x => x.OrganizationAdmin).NotEmpty()
            .ChildRules(x =>
            {
                x.RuleFor(oa => oa.FirstName).NotEmpty();
                x.RuleFor(oa => oa.LastName).NotEmpty();
                x.RuleFor(oa => oa.Email).EmailAddress().NotEmpty();
            });
        RuleFor(x => x.Type)
            .Must(s => OrganizationType.TryFromName(s, out _))
            .WithMessage(x => $"Organization Type '{x}' not supported");
    }
}

internal class CreateOrganizationHandler : IRequestHandler<CreateOrganizationCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateOrganizationHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateOrganizationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Employee organizationAdmin = Domain.Employee.Create(command.OrganizationAdmin.Email,
            command.OrganizationAdmin.FirstName,
            command.OrganizationAdmin.LastName, string.Empty, string.Empty, null);

        Domain.Organization organization = Domain.Organization.Create(command.OrganizationName, organizationAdmin.Id, command.Type);
        // default classification is ambulatory
        Domain.Location location = Domain.Location.Create(organization.Id, command.LocationName, "Ambulatory", true, string.Empty,
            string.Empty, 0, 0, 48, true, null, null);

        LocationEmployee locationEmployee = LocationEmployee.Create(organization.Id, location.Id, organizationAdmin.Id,
            [EmployeeRole.OrganizationAdmin.Name], "#800080", string.Empty, Utils.GenerateRandomId(),
            command.Timestamp, false, null);

        User existingUser = await session.Query<User>()
            .FirstOrDefaultAsync(x => x.PrimaryEmail == command.OrganizationAdmin.Email, token: cancellationToken);

        if (existingUser == null)
        {
            User user =
                User.Create(command.OrganizationAdmin.Email, command.OrganizationAdmin.FirstName,
                    command.OrganizationAdmin.LastName, null, organizationAdmin.Id,
                    [UserRole.Employee.Name]);
            await session.StoreAsync(user, cancellationToken);
        }

        await session.StoreAsync(organization, cancellationToken);
        await session.StoreAsync(location, cancellationToken);
        await session.StoreAsync(organizationAdmin, cancellationToken);
        await session.StoreAsync(locationEmployee, cancellationToken);

        // create a temporary user session for organization creation context
        var tempUser = new UserRequestSession(
            "system", // UserId
            "system-session", // SessionId
            command.OrganizationAdmin.Email, // Email
            command.OrganizationAdmin.FirstName, // FirstName
            command.OrganizationAdmin.LastName, // LastName
            UserRole.Employee.Name, // SelectedUserRole
            organizationAdmin.Id, // EmployeeId
            null, // PatientId
            null, // SelectedOrganizationId - null for new org admin
            null, // OrganizationName - null for new org admin
            null, // OrganizationType
            null, // SelectedLocationId
            null, // LocationName
            null,// SelectedLocationEmployeeRoles
            null); // TimeZone

        await session.StoreAsync(
            new SendInvitationEmail(command.OrganizationAdmin.FirstName, command.OrganizationAdmin.LastName,
                command.OrganizationAdmin.Email, tempUser, locationEmployee.InvitationToken!,
                [EmployeeRole.OrganizationAdmin.Name]), cancellationToken);

        await session.SaveChangesAsync(cancellationToken);

        return organization.Id;
    }
}