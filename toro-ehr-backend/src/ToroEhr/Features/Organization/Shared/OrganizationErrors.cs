using ToroEhr.Infrastructure.ErrorHandling;

namespace ToroEhr.Features.Organization.Shared;

public static class OrganizationErrors
{
    public static AppError NotFoundById(string orgId) => new ("Organizations.NotFound",
        $"The organization with the Id: '{orgId}' was not found");

    public static AppError AlreadyExists(string email) => new("Employee.Exist",
    $"The employee with the email: '{email}' already exists");
}