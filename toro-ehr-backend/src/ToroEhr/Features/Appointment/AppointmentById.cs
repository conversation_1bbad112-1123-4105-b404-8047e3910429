using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Enums;

namespace ToroEhr.Features.Appointment;

public sealed record AppointmentByIdQuery(string Id) : AuthRequest<AppointmentDetailsResponse>;

public sealed record AppointmentDetailsResponse(
    string Id,
    string EmployeeId,
    string LocationId,
    AppointmentStatus Status,
    string PatientFullName,
    DateTimeOffset StartAt,
    int DurationInMinutes);

internal sealed class AppointmentByIdAuth : IAuth<AppointmentByIdQuery, AppointmentDetailsResponse>
{
    public AppointmentByIdAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class AppointmentByIdHandler : IRequestHandler<AppointmentByIdQuery, AppointmentDetailsResponse>
{
    private readonly IDocumentStore _store;

    public AppointmentByIdHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<AppointmentDetailsResponse> Handle(AppointmentByIdQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Appointment appointment = await session
            .Include<Domain.Appointment>(x => x.PatientId)
            .LoadAsync<Domain.Appointment>(query.Id, cancellationToken);

        Guard.AgainstNotFound(appointment,
            new AppError("Appointments.NotFound", $"Appointment with 'id:' {query.Id} not found!"));

        Domain.Patient patient = await session.LoadAsync<Domain.Patient>(appointment.PatientId, cancellationToken);

        return new AppointmentDetailsResponse(appointment.Id, appointment.EmployeeId,
            appointment.LocationId, AppointmentStatus.FromName(appointment.Status), patient.FullName, appointment.StartAt,
            appointment.DurationInMinutes);
    }
}