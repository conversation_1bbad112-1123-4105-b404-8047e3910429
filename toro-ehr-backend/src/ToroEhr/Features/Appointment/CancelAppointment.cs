using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.Appointment;

public sealed record CancelAppointmentCommand(string Id) : AuthRequest<string>;

internal class CancelAppointmentAuth : IAuth<CancelAppointmentCommand, string>;

internal class CancelAppointmentHandler : IRequestHandler<CancelAppointmentCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly EmailService _emailService;
    private readonly UserRequestSession _user;

    public CancelAppointmentHandler(IDocumentStore store, EmailService emailService, Authenticator authenticator)
    {
        _store = store;
        _emailService = emailService;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CancelAppointmentCommand command,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Appointment appointment = await session
            .Include<Domain.Appointment>(x => x.LocationId)
            .LoadAsync<Domain.Appointment>(command.Id, cancellationToken);

        Guard.AgainstNotFound(appointment,
            new("Appointment.NotFound", $"Appointment with 'id:' {command.Id} not found!"));

        Domain.Location location = await session.LoadAsync<Domain.Location>(appointment.LocationId, cancellationToken);
        Guard.AgainstNotFound(location,
            new("Location.NotFound", $"Location with 'id:' {appointment.LocationId} not found!"));

        bool isWithinCheckInWindow =
            appointment.IsWithinCheckInWindow(command.Timestamp, location.CheckInStartOffsetHours);

        await HandleEncounterCancellation(session, appointment, isWithinCheckInWindow, cancellationToken);

        HandleAppointmentCancellation(appointment, isWithinCheckInWindow);

        await session.StoreAsync(appointment, cancellationToken);

        if (_user.IsPatient)
        {
            await CreatePatientCancellationNotification(session, appointment, command.Timestamp, cancellationToken);
        }
        else if (_user.IsEmployee)
        {
            await CreateEmployeeCancellationNotification(session, appointment, command.Timestamp);
        }

        await session.SaveChangesAsync(cancellationToken);

        return appointment.Id;
    }

    private async Task HandleEncounterCancellation(IAsyncDocumentSession session, Domain.Appointment appointment,
        bool isWithinCheckInWindow, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(appointment.EncounterId))
            return;

        Domain.Encounter encounter =
            await session.LoadAsync<Domain.Encounter>(appointment.EncounterId, cancellationToken);

        if (isWithinCheckInWindow)
        {
            encounter.MarkAsCanceledLate();
        }
        else
        {
            encounter.Cancel();
        }

        await session.StoreAsync(encounter, cancellationToken);
    }

    private static void HandleAppointmentCancellation(Domain.Appointment appointment, bool isWithinCheckInWindow)
    {
        if (isWithinCheckInWindow)
        {
            appointment.MarkAsCanceledLate();
        }
        else
        {
            appointment.Cancel();
        }
    }

    private async Task CreatePatientCancellationNotification(IAsyncDocumentSession session,
        Domain.Appointment appointment,
        DateTimeOffset timestamp, CancellationToken cancellationToken)
    {
        Domain.LocationEmployee locationEmployee = await session.Query<Domain.LocationEmployee>()
            .Where(x => x.EmployeeId == appointment.EmployeeId && x.LocationId == appointment.LocationId)
            .FirstOrDefaultAsync(cancellationToken);

        if (locationEmployee != null)
        {
            await NotificationService.CreateAppointmentCanceledByPatientNotification(
                session,
                appointment.EmployeeId,
                appointment.Id,
                locationEmployee.ReceivedNotificationPreferences,
                timestamp.DateTime);
        }
    }

    private async Task CreateEmployeeCancellationNotification(IAsyncDocumentSession session,
        Domain.Appointment appointment,
        DateTimeOffset timestamp)
    {
        // create in-app notification
        await NotificationService.CreateAppointmentCanceledByEmployeeNotification(
            session,
            appointment.PatientId,
            appointment.Id,
            timestamp.DateTime);

        // load entities needed for email/SMS notification
        var patient = await session.LoadAsync<Domain.Patient>(appointment.PatientId);
        var employee = await session.LoadAsync<Domain.Employee>(appointment.EmployeeId);
        var location = await session.LoadAsync<Domain.Location>(appointment.LocationId);

        if (patient != null && employee != null && location != null)
        {
            await StoreCancellationNotification(session, appointment, patient, employee, location);
        }
    }

    private static async Task StoreCancellationNotification(
        IAsyncDocumentSession session,
        Domain.Appointment appointment,
        Domain.Patient patient,
        Domain.Employee employee,
        Domain.Location location)
    {
        if (patient.PreferredContactMethod == "Text" && patient.PhoneNumbers.Any(x => x.IsPrimary))
        {
            await session.StoreAsync(
                new Events.SendSmsAppointmentCanceled(patient, employee, appointment, location.FormattedAddress));
        }
        else
        {
            await session.StoreAsync(
                new Events.SendEmailAppointmentCanceled(patient, employee, appointment, location.FormattedAddress,
                    appointment.DurationInMinutes));
        }
    }
}