using HandlebarsDotNet;
using MediatR;
using ToroEhr.Features.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Services;
using ToroEhr.Templates.Email.AppointmentCanceled;

namespace ToroEhr.Features.Appointment.Events;

public record SendEmailAppointmentCanceled(Domain.Patient Patient, Domain.Employee Employee, 
    Domain.Appointment Appointment, string LocationAddress, int DurationInMinutes) : BaseEventEntity;

public class SendEmailAppointmentCanceledEventHandler : INotificationHandler<SendEmailAppointmentCanceled>
{
    private readonly EmailService _emailService;

    public SendEmailAppointmentCanceledEventHandler(EmailService emailService)
    {
        _emailService = emailService;
    }
    
    public async Task Handle(SendEmailAppointmentCanceled notification, CancellationToken cancellationToken = new())
    {
        var rootDir = Path.Combine("Templates", "Email", "AppointmentCanceled");
        var htmlTemplate = await File.ReadAllTextAsync(Path.Combine(rootDir, "index.html"), cancellationToken);
        var compiledTemplate = Handlebars.Compile(htmlTemplate);

        string email = compiledTemplate(new AppointmentCanceledEmail(Config.Application.Name, Config.Application.Url,
        $"{notification.Patient.FirstName} {notification.Patient.LastName}", $"{notification.Employee.FirstName} {notification.Employee.LastName}",
        notification.LocationAddress, notification.Appointment.StartAt.ToString("dddd, MMMM dd, yyyy h:mm tt"), notification.DurationInMinutes));
        string subject = "Appointment Canceled";

        await _emailService.SendEmailAsync(notification.Patient.Email, subject, email);
    }
}
