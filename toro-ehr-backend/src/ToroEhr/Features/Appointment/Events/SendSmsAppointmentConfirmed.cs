using MediatR;
using NodaTime;
using System.Globalization;
using ToroEhr.Features.Shared;
using ToroEhr.Services;

namespace ToroEhr.Features.Appointment.Events;

public record SendSmsAppointmentConfirmed(Domain.Patient Patient, Domain.Employee Employee, 
    Domain.Appointment Appointment, string LocationAddress, string TimeZoneId) : BaseEventEntity;

public class SendSmsAppointmentConfirmedEventHandler : INotificationHandler<SendSmsAppointmentConfirmed>
{
    private readonly SmsService _smsService;

    public SendSmsAppointmentConfirmedEventHandler(SmsService smsService)
    {
        _smsService = smsService;
    }
    public async Task Handle(SendSmsAppointmentConfirmed notification, CancellationToken cancellationToken = new())
    {
        // Get the time zone from the event
        var tz = DateTimeZoneProviders.Tzdb[notification.TimeZoneId];

        var instant = Instant.FromDateTimeOffset(notification.Appointment.StartAt);
        var zonedDateTime = instant.InZone(tz);

        // Convert to DateTime in that zone
        var localTime = zonedDateTime.ToDateTimeUnspecified();

        // US date format with 12-hour clock
        var formattedDate = localTime.ToString(
            "dddd, MMMM dd, yyyy h:mm tt",
            CultureInfo.InvariantCulture
        );

        // Send SMS
        await _smsService.SendSmsAsync(
            notification.Patient.PhoneNumbers.First(x => x.IsPrimary).Number,
            $"An appointment for {notification.Patient.FullName} with {notification.Employee.FullName} is confirmed.\n" +
            $"Location: {notification.LocationAddress}\n" +
            $"Date: {formattedDate}"
        );
    }
}