using MediatR;
using ToroEhr.Features.Shared;
using ToroEhr.Services;

namespace ToroEhr.Features.Appointment.Events;

public record SendSmsAppointmentCanceled(Domain.Patient Patient, Domain.Employee Employee, 
    Domain.Appointment Appointment, string LocationAddress) : BaseEventEntity;

public class SendSmsAppointmentCanceledEventHandler : INotificationHandler<SendSmsAppointmentCanceled>
{
    private readonly SmsService _smsService;

    public SendSmsAppointmentCanceledEventHandler(SmsService smsService)
    {
        _smsService = smsService;
    }
    
    public async Task Handle(SendSmsAppointmentCanceled notification, CancellationToken cancellationToken = new())
    {
        await _smsService.SendSmsAsync(notification.Patient.PhoneNumbers.First(x => x.IsPrimary).Number,
        $"Your appointment with {notification.Employee.FullName} has been canceled. \n Canceled Appointment Details \n Location: {notification.LocationAddress} \n Date: {notification.Appointment.StartAt.ToString("dddd, MMMM dd, yyyy h:mm tt")} \n Please contact us to schedule a new appointment if needed.");
    }
}
