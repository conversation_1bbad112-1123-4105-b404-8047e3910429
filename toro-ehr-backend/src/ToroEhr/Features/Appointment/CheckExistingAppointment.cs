using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Appointment;

public sealed record CheckExistingAppointmentQuery(
    string PatientId, 
    string EmployeeId, 
    DateTimeOffset Date) : AuthRequest<ExistingAppointmentResponse?>;

public sealed record ExistingAppointmentResponse(
    string AppointmentId,
    DateTimeOffset StartAt,
    DateTimeOffset EndAt,
    string EmployeeName,
    string LocationName,
    AppointmentStatus Status);

internal sealed class CheckExistingAppointmentAuth : IAuth<CheckExistingAppointmentQuery, ExistingAppointmentResponse?>
{
    public CheckExistingAppointmentAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        // allow both patients and employees to check for existing appointments
        if (!user.IsPatient)
        {
            AuthorizationGuard.AffirmIsEmployee(user);
        }
    }
}

internal sealed class CheckExistingAppointmentHandler : IRequestHandler<CheckExistingAppointmentQuery, ExistingAppointmentResponse?>
{
    private readonly IDocumentStore _store;

    public CheckExistingAppointmentHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<ExistingAppointmentResponse?> Handle(CheckExistingAppointmentQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // check if patient already has an appointment on this date
        var existingAppointment = await session
            .Query<Domain.Appointment>()
            .Include(x => x.EmployeeId)
            .Include(x => x.LocationId)
            .Where(x => x.PatientId == query.PatientId && 
                       x.StartAt >= query.Date.Date && 
                       x.StartAt < query.Date.AddDays(1).Date &&
                       x.Status != AppointmentStatus.Canceled.Name && 
                       x.Status != AppointmentStatus.CanceledLate.Name && 
                       x.Status != AppointmentStatus.Missed.Name)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingAppointment == null)
        {
            return null;
        }

        var employee = await session.LoadAsync<Domain.Employee>(existingAppointment.EmployeeId, cancellationToken);
        var location = await session.LoadAsync<Domain.Location>(existingAppointment.LocationId, cancellationToken);

        return new ExistingAppointmentResponse(
            existingAppointment.Id,
            existingAppointment.StartAt,
            existingAppointment.EndAt,
            $"{employee.FirstName} {employee.LastName}",
            location.Name,
            AppointmentStatus.FromName(existingAppointment.Status));
    }
}
