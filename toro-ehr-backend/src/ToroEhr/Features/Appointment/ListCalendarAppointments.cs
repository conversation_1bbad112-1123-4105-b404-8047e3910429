using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Enums;
using ToroEhr.Indexes;
using ToroEhr.Features.Appointment.Shared;

namespace ToroEhr.Features.Appointment;

public sealed record ListCalendarAppointmentsQuery(string? EmployeeId, DateTimeOffset? Start, DateTimeOffset? End)
    : AuthRequest<List<CalendarAppointmentResponse>>;

public sealed record CalendarAppointmentResponse(
    string Id,
    string Title,
    DateTimeOffset Start,
    DateTimeOffset End,
    string Color,
    CalendarAppointmentDetailsResponse ExtendedProps);

internal sealed class
    ListCalendarAppointmentsAuth : IAuth<ListCalendarAppointmentsQuery, List<CalendarAppointmentResponse>>
{
    public ListCalendarAppointmentsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    ListCalendarAppointmentsHandler : IRequestHandler<ListCalendarAppointmentsQuery, List<CalendarAppointmentResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListCalendarAppointmentsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<CalendarAppointmentResponse>> Handle(ListCalendarAppointmentsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Employees_ByLocation.Entry> employeeEntriesQuery = session
            .Query<Employees_ByLocation.Entry, Employees_ByLocation>()
            .Where(x => x.LocationId == _user.SelectedLocationId)
            .ProjectInto<Employees_ByLocation.Entry>();

        if (query.EmployeeId.IsNotNullOrWhiteSpace())
        {
            employeeEntriesQuery = employeeEntriesQuery.Where(x => x.EmployeeId == query.EmployeeId);
        }

        var employeeEntries = await employeeEntriesQuery.ToListAsync(cancellationToken);

        var employeeIds = employeeEntries.Select(x => x.EmployeeId).ToList();


        IRavenQueryable<Domain.Appointment> appointmentsQuery = session
            .Query<Domain.Appointment>()
            .Where(x => x.EmployeeId.In(employeeIds) && x.Status != AppointmentStatus.Canceled.Name &&
                        x.Status != AppointmentStatus.CanceledLate.Name && x.Status != AppointmentStatus.Missed.Name);

        if (query.Start.HasValue)
        {
            appointmentsQuery = appointmentsQuery.Where(x => x.StartAt > query.Start);
        }

        if (query.End.HasValue)
        {
            appointmentsQuery = appointmentsQuery.Where(x => x.EndAt < query.End);
        }

        var appointments = await appointmentsQuery
            .Include(x => x.EmployeeId)
            .Include(x => x.PatientId)
            .Include(x => x.LocationId)
            .ToListAsync(cancellationToken);

        var result = new List<CalendarAppointmentResponse>();

        foreach (var appointment in appointments)
        {
            var employee = await session.LoadAsync<Domain.Employee>(appointment.EmployeeId);
            var patient = await session.LoadAsync<Domain.Patient>(appointment.PatientId);
            var location = await session.LoadAsync<Domain.Location>(appointment.LocationId);

            var calendarColor = employeeEntries.First(x => x.EmployeeId == appointment.EmployeeId).CalendarColor;

            result.Add(new CalendarAppointmentResponse(appointment.Id,
                "Appointment", appointment.StartAt, appointment.StartAt.AddMinutes(appointment.DurationInMinutes),
                calendarColor ?? "green",
                new CalendarAppointmentDetailsResponse(patient.FullName, employee.ShortName, location.Name,
                    calendarColor ?? "green")));
        }

        return result;
    }
}