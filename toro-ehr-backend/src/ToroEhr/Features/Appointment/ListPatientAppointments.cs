using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Enums;
using System.Linq;

namespace ToroEhr.Features.Appointment;

public sealed record ListPatientAppointmentsQuery(PagedSearchParams PagedSearchParams, AppointmentTimeFilter? TimeFilter = null)
    : AuthRequest<PaginatedList<AppointmentResponse>>;

public sealed record AppointmentResponse(
    string Id,
    string EncounterId,
    string EmployeeName,
    string Location,
    string LocationAddress,
    DateTimeOffset StartAt,
    int DurationInMinutes,
    AppointmentStatus Status,
    DateTimeOffset CheckInAvailableAt,
    int CheckInStartOffsetHours,
    decimal MissedAppointmentFee,
    string TimeZone,
    AppointmentTimeFilter? CurrentTimeFilter = null); // metadata for frontend

internal sealed class ListLocationsAuth : IAuth<ListPatientAppointmentsQuery, PaginatedList<AppointmentResponse>>
{
    public ListLocationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class
    ListLocationsHandler : IRequestHandler<ListPatientAppointmentsQuery, PaginatedList<AppointmentResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListLocationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<AppointmentResponse>> Handle(ListPatientAppointmentsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Appointments_ByParticipant.Entry> dbQuery = session
            .Query<Appointments_ByParticipant.Entry, Appointments_ByParticipant>()
            .Include(x => x.AppointmentId)
            .ProjectInto<Appointments_ByParticipant.Entry>();

        if (_user.IsPatient)
        {
            dbQuery = dbQuery.Where(x => x.PatientId == _user.PatientId);
        }

        var timeFilter = query.TimeFilter ?? AppointmentTimeFilter.All;

        if (timeFilter == AppointmentTimeFilter.Upcoming)
        {
            var upcomingStatuses = AppointmentStatus.Upcoming.Select(s => s.Name).ToArray();
            dbQuery = dbQuery
                .Where(x => x.Status.In(upcomingStatuses))
                .OrderBy(x => x.StartAt);
        }
        else if (timeFilter == AppointmentTimeFilter.Past)
        {
            var pastStatuses = AppointmentStatus.Past.Select(s => s.Name).ToArray();
            dbQuery = dbQuery
                .Where(x => x.Status.In(pastStatuses))
                .OrderByDescending(x => x.StartAt);
        }
        else
        {
            dbQuery = dbQuery.OrderByDescending(x => x.StartAt);
        }

        var queryItems = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        var appointments =
            await session.LoadAsync<Domain.Appointment>(queryItems.Select(x => x.AppointmentId).ToList(),
                cancellationToken);

        IEnumerable<AppointmentResponse> resultItems = queryItems.Select(item =>
        {
            var appointment = appointments[item.AppointmentId];
            return new AppointmentResponse(appointment.Id, item.EncounterId, item.EmployeeFullName,
                item.LocationName, item.LocationAddress, appointment.StartAt, appointment.DurationInMinutes, AppointmentStatus.FromName(appointment.Status),
                appointment.StartAt.AddHours(-item.CheckInStartOffsetHours), item.CheckInStartOffsetHours,
                item.MissedAppointmentFee, item.TimeZone, timeFilter);
        }).ToList();

        return PaginatedList<AppointmentResponse>.Create(resultItems, stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize);
    }
}