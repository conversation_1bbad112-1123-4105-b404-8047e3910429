using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using SessionOptions = Raven.Client.Documents.Session.SessionOptions;

namespace ToroEhr.Features.Patient.PatientInsurance;

public sealed record GetPatientInsuranceInfoQuery(string? PatientId) : AuthRequest<PatientInsuranceInfoResponse>;

public sealed record PatientInsuranceInfoResponse(
    bool HasInsurance,
    decimal? PrimaryCopay);

internal sealed class GetPatientInsuranceInfoAuth : IAuth<GetPatientInsuranceInfoQuery, PatientInsuranceInfoResponse>
{
    public GetPatientInsuranceInfoAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        // allow both patients and employees to access this information
        if (!user.IsPatient)
        {
            AuthorizationGuard.AffirmIsEmployee(user);
        }
    }
}

internal sealed class GetPatientInsuranceInfoHandler : IRequestHandler<GetPatientInsuranceInfoQuery, PatientInsuranceInfoResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetPatientInsuranceInfoHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PatientInsuranceInfoResponse> Handle(GetPatientInsuranceInfoQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession(new SessionOptions() { NoTracking = true });

        // use provided PatientId if user is employee, otherwise use authenticated patient's ID
        var patientId = query.PatientId ?? _user.PatientId;
        
        if (string.IsNullOrEmpty(patientId))
        {
            return new PatientInsuranceInfoResponse(false, null);
        }

        IEnumerable<Domain.PatientInsurance> insurances = await session.Query<Domain.PatientInsurance>()
            .Where(x => x.PatientId == patientId).ToListAsync(token: cancellationToken);

        var insuranceList = insurances.ToList();
        var hasInsurance = insuranceList.Any();
        
        // find primary insurance and get its copay
        var primaryInsurance = insuranceList
            .FirstOrDefault(x => x.Order.Equals("Primary", StringComparison.OrdinalIgnoreCase));
        
        var primaryCopay = primaryInsurance?.Copay;

        return new PatientInsuranceInfoResponse(hasInsurance, primaryCopay);
    }
}
