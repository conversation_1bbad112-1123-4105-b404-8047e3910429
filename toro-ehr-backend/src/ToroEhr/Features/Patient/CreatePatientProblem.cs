using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient;

public record CreatePatientProblemCommand(string PatientId, string EncounterId, string Condition, string ClinicalConditionStatus, string ConditionVerificationStatus,
        DateTime DiagnosisDate, DateTime? Abatement) : AuthRequest<string>;

internal class CreatePatientProblemAuth : IAuth<CreatePatientProblemCommand, string>
{
    public CreatePatientProblemAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class CreatePatientProblemValidator : AbstractValidator<CreatePatientProblemCommand>
{
    public CreatePatientProblemValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.Condition).NotEmpty();
        RuleFor(x => x.ClinicalConditionStatus).NotEmpty();
        RuleFor(x => x.ConditionVerificationStatus).NotEmpty();
        RuleFor(x => x.DiagnosisDate).NotEmpty();
    }
}

internal class CreatePatientProblemHandler : IRequestHandler<CreatePatientProblemCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreatePatientProblemHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreatePatientProblemCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.PatientProblem patientProblem = Domain.PatientProblem.Create(command.PatientId, command.EncounterId, command.Condition, 
            command.ClinicalConditionStatus, command.ConditionVerificationStatus, DateOnly.FromDateTime(command.DiagnosisDate), command.Abatement != null ?
            DateOnly.FromDateTime(command.Abatement.Value) : null, _user.EmployeeId!);

        await session.StoreAsync(patientProblem, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return patientProblem.Id;
    }
}