using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient;

public sealed record DeletePatientPaymentCardCommand(string CardId) : AuthRequest<Unit>;

internal sealed class DeletePatientPaymentCardAuth : IAuth<DeletePatientPaymentCardCommand, Unit>
{
    public DeletePatientPaymentCardAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class DeletePatientPaymentCardValidator : AbstractValidator<DeletePatientPaymentCardCommand>
{
    public DeletePatientPaymentCardValidator()
    {
        RuleFor(x => x.CardId).NotEmpty();
    }
}

internal sealed class DeletePatientPaymentCardHandler : IRequestHandler<DeletePatientPaymentCardCommand, Unit>
{
    private readonly IDocumentStore _store;

    public DeletePatientPaymentCardHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
    }

    public async Task<Unit> Handle(DeletePatientPaymentCardCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.PatientPaymentCard paymentCard = await session.LoadAsync<Domain.PatientPaymentCard>(command.CardId, cancellationToken);

        if (paymentCard == null) return Unit.Value;

        session.Delete(paymentCard.Id);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}
