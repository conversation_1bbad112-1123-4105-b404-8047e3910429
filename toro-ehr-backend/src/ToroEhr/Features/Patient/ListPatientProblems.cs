using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient;

public record ListPatientProblemsQuery(string PatientId) : AuthRequest<List<PatientProblemResponse>>;

internal class ListPatientProblemsAuth : IAuth<ListPatientProblemsQuery, List<PatientProblemResponse>>
{
    public ListPatientProblemsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public record PatientProblemResponse(
    string Id,
    string PatientId, 
    string ConditionDescription, 
    string ClinicalStatus, 
    string VerificationStatus,
    DateOnly DiagnosisDate, 
    DateOnly? Abatement);

internal class ListPatientProblemsHandler : IRequestHandler<ListPatientProblemsQuery, List<PatientProblemResponse>>
{
    private readonly IDocumentStore _store;

    public ListPatientProblemsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
    }

    public async Task<List<PatientProblemResponse>> Handle(ListPatientProblemsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Patient patient = await session.LoadAsync<Domain.Patient>(query.PatientId, cancellationToken);
        Guard.AgainstNotFound(patient, PatientErrors.NotFoundById(query.PatientId));

        List<Domain.PatientProblem> patientProblems = await session.Query<Domain.PatientProblem>()
            .Include(x => x.Condition)
            .Where(x => x.PatientId == patient.Id)
            .ToListAsync(cancellationToken);

        var results = new List<PatientProblemResponse>();

        foreach (var problem in patientProblems)
        {
            var icd10 = await session.LoadAsync<Domain.Icd10>(problem.Condition);

            results.Add(new PatientProblemResponse(problem.Id, problem.PatientId, icd10.DisplayName, problem.ClinicalConditionStatus,
                problem.ConditionVerificationStatus, problem.DiagnosisDate, problem.Abatement));
        }

        return results;
    }
}