using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using AddressResponse = ToroEhr.Features.Shared.AddressResponse;

namespace ToroEhr.Features.Patient;

public record GetPatientProfileQuery(string? PatientId) : AuthRequest<PatientProfileResponse>;

internal class GetPatientProfileAuth : IAuth<GetPatientProfileQuery, PatientProfileResponse>
{
    public GetPatientProfileAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatientOrEmployee(user);
    }
}

public record PatientProfileResponse(
    string Mrn,
    string FirstName,
    string LastName,
    string? MiddleName,
    string? Suffix,
    string? PreferredName,
    string? PreviousFirstName,
    string? PreviousLastName,
    string? PreferredLanguage,
    DateTime Birthday,
    string? BirthSex,
    string? GenderIdentity,
    string? SexualOrientation,
    string? Race,
    string? Ethnicity,
    string? TribalAffiliation,
    decimal? HeightInCm,
    decimal? WeightInKg,
    IEnumerable<DocumentResponse> Documents,
    AddressResponse? Address,
    AddressResponse? PreviousAddress,
    string? SocialSecurityNumber,
    string? PreferredContactMethod,
    string? PreferredContactName,
    IEnumerable<EmailAddressResponse> Emails,
    IEnumerable<PhoneNumberResponse> Phones,
    IEnumerable<EmergencyContactResponse> EmergencyContacts,
    IEnumerable<PatientMedicationResponse> Medications,
    IEnumerable<PatientAllergyResponse> Allergies);

public record EmergencyContactResponse(string Name, string Relationship, string PhoneNumber, bool Primary);

public record PhoneNumberResponse(string Number, string Type, bool Primary);

public record EmailAddressResponse(string Email, bool Primary);

public record DocumentResponse(string DocId, string DocumentType, DateTime CreatedAt, IEnumerable<string> FilePaths);

public record PatientMedicationResponse(string Code, string DisplayName);

public record PatientAllergyResponse(string Code, string DisplayName, string Reaction, string Severity);

internal class Handler : IRequestHandler<GetPatientProfileQuery, PatientProfileResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public Handler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PatientProfileResponse> Handle(GetPatientProfileQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var patientId = query.PatientId ?? _user.PatientId;
        //todo: if query.PatientId not null add check for practitioner - patient relations

        Domain.Patient patient = await session.LoadAsync<Domain.Patient>(patientId, cancellationToken);
        Guard.AgainstNotFound(patient, PatientErrors.NotFoundById(patientId!));
        
        return new PatientProfileResponse(
            patient.Mrn,
            patient.FirstName,
            patient.LastName,
            patient.MiddleName,
            patient.Suffix,
            patient.PreferredName,
            patient.PreviousFirstName,
            patient.PreviousLastName,
            patient.PreferredLanguage,
            patient.Birthday,
            patient.BirthSex,
            patient.GenderIdentity,
            patient.SexualOrientation,
            patient.Race,
            patient.Ethnicity,
            patient.TribalAffiliation,
            patient.HeightInCm,
            patient.WeightInKg,
            patient.Documents.Select(x => new DocumentResponse(
                    x.Id,
                    x.Type,
                    x.CreatedAt,
                    x.FilePaths.Select(fp => Utils.GenerateS3PublicFileUrl(Config.S3.AppFilesBucketName, fp))))
                .ToList(),
            patient.Address != null
                ? new AddressResponse(patient.Address.Street, patient.Address.City, patient.Address.State,
                    patient.Address.ZipCode)
                : null,
            patient.PreviousAddress != null
                ? new AddressResponse(patient.PreviousAddress.Street, patient.PreviousAddress.City, patient.PreviousAddress.State,
                    patient.PreviousAddress.ZipCode)
                : null,
            patient.SocialSecurityNumber,
            patient.PreferredContactMethod,
            patient.PreferredContactName,
            patient.Emails.Select(x => new EmailAddressResponse(x.Email, x.IsPrimary)).ToList(),
            patient.PhoneNumbers.Select(x => new PhoneNumberResponse(x.Number, x.Type, x.IsPrimary)).ToList(),
            patient.EmergencyContacts
                .Select(x => new EmergencyContactResponse(x.Name, x.Relationship, x.PhoneNumber, x.Primary)).ToList(),
            patient.Medications.Select(x => new PatientMedicationResponse(x.Code, x.DisplayName)).ToList(),
            patient.Allergies.Select(x => new PatientAllergyResponse(x.Code, x.DisplayName, x.Reaction, x.Severity))
                .ToList()
        );
    }
}