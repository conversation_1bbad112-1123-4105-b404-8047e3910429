using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Infrastructure;

namespace ToroEhr.Features.Patient;

public record ListPatientCommunicationMessagesQuery(PagedSearchParams PagedSearchParams) : AuthRequest<PaginatedList<PatientCommunicationMessageResponse>>;

internal class ListPatientCommunicationMessagesAuth : IAuth<ListPatientCommunicationMessagesQuery, PaginatedList<PatientCommunicationMessageResponse>>
{
    public ListPatientCommunicationMessagesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public record PatientCommunicationMessageResponse(
    string Id,
    string? EncounterId,
    string Subject,
    string Message,
    bool SentByPatient,
    string Practitioner,
    bool Seen,
    List<string?> Attachments,
    string Type,
    DateTimeOffset SentAt);


internal class GetPatientCommunicationMessagesHandler : IRequestHandler<ListPatientCommunicationMessagesQuery, PaginatedList<PatientCommunicationMessageResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetPatientCommunicationMessagesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<PatientCommunicationMessageResponse>> Handle(ListPatientCommunicationMessagesQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();


        List<Communication_ByEncounter.Entry> entries = await session
            .Query<Communication_ByEncounter.Entry, Communication_ByEncounter>()
            .Include(e => e.Id)
            .Include(e => e.PractitionerId)
            .Where(x => x.PatientId == _user.PatientId)
            .OrderByDescending(x => x.SentAt)
            .ProjectInto<Communication_ByEncounter.Entry>()
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(cancellationToken);

        var messages = new List<PatientCommunicationMessageResponse>();

        foreach (var entry in entries)
        {
            var message = await session.LoadAsync<Domain.CommunicationEntity>(entry.Id, cancellationToken);
            var practitioner = await session.LoadAsync<Domain.Employee>(entry.PractitionerId, cancellationToken);

            messages.Add(new PatientCommunicationMessageResponse(entry.Id, entry.EncounterId, entry.Subject, entry.Message, message.Sender.SentByPatient, 
                practitioner.FullName, message.SeenByRecipient, entry.Attachments.Select(fp => Utils.GenerateS3PublicFileUrl(Config.S3.AppFilesBucketName, fp)).ToList(), 
                entry.Type, entry.SentAt));
        }

        return PaginatedList<PatientCommunicationMessageResponse>.Create(
            messages,
            stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize);
    }
}