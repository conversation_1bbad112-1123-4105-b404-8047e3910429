using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient;

public sealed record GetPatientByMrnQuery(string Mrn) : AuthRequest<PatientResponse?>;

internal class GetPatientByMrnHandler : IRequestHandler<GetPatientByMrnQuery, PatientResponse?>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetPatientByMrnHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PatientResponse?> Handle(GetPatientByMrnQuery query, CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        // First check if patient exists with this MRN in the current organization
        var patientEntry = await session
            .Query<Indexes.Patient_ByOrganization.Entry, Indexes.Patient_ByOrganization>()
            .Where(x => x.OrganizationId == _user.SelectedOrganizationId && x.Mrn == query.Mrn)
            .ProjectInto<Indexes.Patient_ByOrganization.Entry>()
            .FirstOrDefaultAsync(cancellationToken);

        if (patientEntry == null)
            return null;

        var patient = await session.LoadAsync<Domain.Patient>(patientEntry.PatientId, cancellationToken);
        Guard.AgainstNotFound(patient, PatientErrors.NotFoundById(patientEntry.PatientId));

        return new PatientResponse(
            patient.Id,
            patient.Mrn,
            patient.Email,
            patient.FirstName,
            patient.LastName,
            patient.Birthday,
            patient.PhoneNumbers.First(x => x.IsPrimary).Number,
            patient.PreferredContactMethod
        );
    }
}
