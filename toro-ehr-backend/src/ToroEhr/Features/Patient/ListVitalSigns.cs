using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient;

public sealed record ListVitalSignsQuery(string PatientId) : AuthRequest<List<VitalSignResponse>>;

internal sealed class ListVitalSignsAuth : IAuth<ListVitalSignsQuery, List<VitalSignResponse>>
{
    public ListVitalSignsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class ListVitalSignsHandler : IRequestHandler<ListVitalSignsQuery, List<VitalSignResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListVitalSignsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<VitalSignResponse>> Handle(ListVitalSignsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var distinctTimestamps = await session.Query<Domain.VitalSign>()
            .Where(x => x.PatientId == query.PatientId && x.RecordedDate != null)
            .OrderByDescending(x => x.RecordedDate)
            .Select(x => x.RecordedDateTimestamp)
            .Distinct()
            .Take(10)
            .ToListAsync(cancellationToken);

        var vitalSigns = await session.Query<Domain.VitalSign>()
            .Include(x => x.RecordedBy)
            .Include(x => x.CreatedBy)
            .Include(x => x.ModifiedBy)
            .Where(x => x.RecordedDate == null || x.RecordedDateTimestamp.In(distinctTimestamps))
            .Where(x => x.PatientId == query.PatientId && x.Status != "cancel" && x.Status != "entered-in-error")
            .ToListAsync(cancellationToken);


        var vitalSingsResponse = new List<VitalSignResponse>();

        foreach (var vs in vitalSigns)
        {
            var recordedBy = await session.LoadAsync<Domain.Employee>(vs.RecordedBy, cancellationToken);
            var createdBy = await session.LoadAsync<Domain.Employee>(vs.CreatedBy, cancellationToken);
            var modifiedBy = await session.LoadAsync<Domain.Employee>(vs.ModifiedBy, cancellationToken);

            vitalSingsResponse.Add(new VitalSignResponse(
            vs.Id,
            vs.RecordedDate,
            recordedBy?.FullName,
            vs.Type,
            vs.Value,
            vs.Status,
            vs.CreatedAt,
            vs.ModifiedAt,
            createdBy.FullName,
            modifiedBy?.FullName));
        }

        return vitalSingsResponse;
    }
}