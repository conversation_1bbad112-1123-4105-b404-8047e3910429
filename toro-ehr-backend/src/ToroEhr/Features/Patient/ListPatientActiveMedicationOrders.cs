using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Features.Patient;

public sealed record ListPatientActiveMedicationOrdersQuery(string PatientId)
    : AuthRequest<List<PatientActiveMedication>>;

public sealed record PatientActiveMedication(string Name, string Frequency, string Duration, 
    string Instructions, string Practitioner);

internal sealed class ListPatientActiveMedicationOrdersAuth : IAuth<ListPatientActiveMedicationOrdersQuery, List<PatientActiveMedication>>
{
    public ListPatientActiveMedicationOrdersAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

internal sealed class ListPatientActiveMedicationOrdersHandler : IRequestHandler<ListPatientActiveMedicationOrdersQuery, List<PatientActiveMedication>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPatientActiveMedicationOrdersHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<PatientActiveMedication>> Handle(ListPatientActiveMedicationOrdersQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // Load medication orders using index
        List<Orders_ByEncounter.Entry> medicationOrderEntries = await session
            .Query<Orders_ByEncounter.Entry, Orders_ByEncounter>()
            .Include(x => x.OrderId)
            .Include(x => x.PractitionerId)
            .Where(x => x.PatientId == query.PatientId && x.Status == OrderStatus.Active.Name && (x.OrderType == nameof(OrderMedication) || x.OrderType == nameof(OrderBundle)))
            .ProjectInto<Orders_ByEncounter.Entry>()
            .ToListAsync(cancellationToken);

        var result = new List<PatientActiveMedication>();

        foreach (var entry in medicationOrderEntries)
        {
            var practitioner = await session.LoadAsync<Domain.Employee>(entry.PractitionerId, cancellationToken);

            if (entry.OrderType == nameof(OrderMedication))
            {
                var medicationOrder = await session.LoadAsync<OrderMedication>(entry.OrderId, cancellationToken);
                result.Add(new PatientActiveMedication(medicationOrder.Name, medicationOrder.Frequency, 
                    medicationOrder.Duration, medicationOrder.Frequency, practitioner?.FullName ?? string.Empty));
            }
            else if (entry.OrderType == nameof(OrderBundle))
            {
                var bundleOrder = await session.LoadAsync<OrderBundle>(entry.OrderId, cancellationToken);

                foreach (var nestedOrder in bundleOrder.Orders)
                {
                    if (nestedOrder is OrderMedication nestedMedicationOrder)
                    {

                        result.Add(new PatientActiveMedication(nestedMedicationOrder.Name, nestedMedicationOrder.Frequency, 
                            nestedMedicationOrder.Duration, nestedMedicationOrder.Frequency, practitioner.FullName));
                    }
                }
            }
        }

        return result;
    }
}